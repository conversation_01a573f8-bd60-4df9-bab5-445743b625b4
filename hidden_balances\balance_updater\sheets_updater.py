"""
Google Sheets integration for updating balance data.
"""

import os
import time
from typing import List, Optional, Dict, Any
from datetime import datetime

try:
    import gspread
    from google.oauth2.service_account import Credentials
    SHEETS_AVAILABLE = True
except ImportError:
    SHEETS_AVAILABLE = False

from .config import Config
from .balance_aggregator import AccountBalance


class SheetsUpdater:
    """Service for updating Google Sheets with balance data."""
    
    def __init__(self, config: Config):
        """
        Initialize Google Sheets updater.
        
        Args:
            config: Configuration object with Google Sheets settings
            
        Raises:
            ImportError: If required Google Sheets libraries are not installed
            ValueError: If Google Sheets configuration is missing
        """
        if not SHEETS_AVAILABLE:
            raise ImportError(
                "Google Sheets libraries not available. "
                "Install with: pip install gspread google-auth"
            )
        
        self.config = config
        
        if not config.google_sheets_id:
            raise ValueError("Google Sheets ID not configured")
        
        if not config.google_credentials_path or not os.path.exists(config.google_credentials_path):
            raise ValueError(f"Google credentials file not found: {config.google_credentials_path}")
        
        # Initialize Google Sheets client
        self._init_sheets_client()
    
    def _init_sheets_client(self):
        """Initialize the Google Sheets client with service account credentials."""
        # Define the scope for Google Sheets API
        scope = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]
        
        # Load credentials from service account file
        creds = Credentials.from_service_account_file(
            self.config.google_credentials_path, 
            scopes=scope
        )
        
        # Initialize gspread client
        self.gc = gspread.authorize(creds)
        
        # Open the spreadsheet
        try:
            self.spreadsheet = self.gc.open_by_key(self.config.google_sheets_id)
        except gspread.SpreadsheetNotFound:
            raise ValueError(f"Spreadsheet not found with ID: {self.config.google_sheets_id}")
    
    def _get_or_create_worksheet(self, sheet_name: str):
        """Get existing worksheet or create a new one."""
        try:
            return self.spreadsheet.worksheet(sheet_name)
        except gspread.WorksheetNotFound:
            # Create new worksheet
            return self.spreadsheet.add_worksheet(title=sheet_name, rows=1000, cols=20)
    
    def _prepare_balance_data(self, balances: List[AccountBalance], summary_stats: Dict[str, Any]) -> List[List[str]]:
        """
        Prepare balance data for Google Sheets format.
        
        Args:
            balances: List of AccountBalance objects
            summary_stats: Summary statistics dictionary
            
        Returns:
            List of rows for Google Sheets
        """
        # Header row
        headers = [
            "Email",
            "Type",
            "Spot BTC",
            "Spot USDT",
            "Margin BTC", 
            "Margin USDT",
            "Futures BTC",
            "Futures USDT",
            "Total BTC",
            "Total USDT"
        ]
        
        rows = [headers]
        
        # Data rows
        for balance in balances:
            # Only show accounts with balances or if filtering is enabled
            filter_emails = set(self.config.emails) if self.config.emails else None
            show_account = filter_emails or balance.total_btc > 0
            
            if show_account:
                row = [
                    balance.email,
                    balance.account_type,
                    str(balance.spot_btc),
                    str(balance.spot_usdt),
                    str(balance.margin_btc),
                    str(balance.margin_usdt),
                    str(balance.futures_btc),
                    str(balance.futures_usdt),
                    str(balance.total_btc),
                    str(balance.total_usdt)
                ]
                rows.append(row)
        
        # Add summary rows
        rows.append([])  # Empty row
        rows.append(["SUMMARY"])
        rows.append(["Total Accounts", str(summary_stats["total_accounts"])])
        rows.append(["Regular Accounts", str(summary_stats["regular_accounts"])])
        rows.append(["Managed Accounts", str(summary_stats["managed_accounts"])])
        rows.append(["Grand Total BTC", str(summary_stats["grand_total_btc"])])
        rows.append(["Grand Total USDT", str(summary_stats["grand_total_usdt"])])
        rows.append(["BTC Price", f"${summary_stats['btc_price']:,}"])
        rows.append(["Last Updated", summary_stats["timestamp"]])
        
        return rows
    
    def update_balances(self, balances: List[AccountBalance], summary_stats: Dict[str, Any]) -> bool:
        """
        Update Google Sheets with current balance data.
        
        Args:
            balances: List of AccountBalance objects
            summary_stats: Summary statistics dictionary
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Get or create the worksheet
            worksheet = self._get_or_create_worksheet(self.config.sheet_name)
            
            # Prepare data
            data = self._prepare_balance_data(balances, summary_stats)
            
            # Clear existing data
            worksheet.clear()
            
            # Update with new data
            worksheet.update('A1', data)
            
            # Format the header row (bold)
            worksheet.format('A1:J1', {'textFormat': {'bold': True}})
            
            # Format currency columns
            currency_format = {'numberFormat': {'type': 'CURRENCY', 'pattern': '$#,##0.00'}}
            worksheet.format('D:D', currency_format)  # Spot USDT
            worksheet.format('F:F', currency_format)  # Margin USDT
            worksheet.format('H:H', currency_format)  # Futures USDT
            worksheet.format('J:J', currency_format)  # Total USDT
            
            print(f"Successfully updated Google Sheets: {self.config.google_sheets_id}")
            return True
            
        except Exception as e:
            print(f"Error updating Google Sheets: {e}")
            return False
    
    def create_historical_snapshot(self, balances: List[AccountBalance], summary_stats: Dict[str, Any]) -> bool:
        """
        Create a historical snapshot in a separate worksheet.
        
        Args:
            balances: List of AccountBalance objects
            summary_stats: Summary statistics dictionary
            
        Returns:
            True if snapshot was created successfully, False otherwise
        """
        try:
            # Create worksheet name with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            sheet_name = f"Snapshot_{timestamp}"
            
            # Create new worksheet
            worksheet = self.spreadsheet.add_worksheet(title=sheet_name, rows=1000, cols=20)
            
            # Prepare and update data
            data = self._prepare_balance_data(balances, summary_stats)
            worksheet.update('A1', data)
            
            # Format the header row
            worksheet.format('A1:J1', {'textFormat': {'bold': True}})
            
            print(f"Created historical snapshot: {sheet_name}")
            return True
            
        except Exception as e:
            print(f"Error creating historical snapshot: {e}")
            return False
