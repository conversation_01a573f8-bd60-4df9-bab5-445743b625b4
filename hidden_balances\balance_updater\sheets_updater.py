"""
Google Sheets integration for updating balance data.
"""

import os
import time
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

try:
    import gspread
    from google.oauth2.service_account import Credentials
    SHEETS_AVAILABLE = True
except ImportError:
    SHEETS_AVAILABLE = False

from .config import Config
from .balance_aggregator import AccountBalance


class SheetsUpdater:
    """Service for updating Google Sheets with balance data."""
    
    def __init__(self, config: Config):
        """
        Initialize Google Sheets updater.
        
        Args:
            config: Configuration object with Google Sheets settings
            
        Raises:
            ImportError: If required Google Sheets libraries are not installed
            ValueError: If Google Sheets configuration is missing
        """
        if not SHEETS_AVAILABLE:
            raise ImportError(
                "Google Sheets libraries not available. "
                "Install with: pip install gspread google-auth"
            )
        
        self.config = config
        
        if not config.google_sheets_id:
            raise ValueError("Google Sheets ID not configured")
        
        if not config.google_credentials_path or not os.path.exists(config.google_credentials_path):
            raise ValueError(f"Google credentials file not found: {config.google_credentials_path}")
        
        # Initialize Google Sheets client
        self._init_sheets_client()
    
    def _init_sheets_client(self):
        """Initialize the Google Sheets client with service account credentials."""
        # Define the scope for Google Sheets API
        scope = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]
        
        # Load credentials from service account file
        creds = Credentials.from_service_account_file(
            self.config.google_credentials_path, 
            scopes=scope
        )
        
        # Initialize gspread client
        self.gc = gspread.authorize(creds)
        
        # Open the spreadsheet
        try:
            self.spreadsheet = self.gc.open_by_key(self.config.google_sheets_id)
        except gspread.SpreadsheetNotFound:
            raise ValueError(f"Spreadsheet not found with ID: {self.config.google_sheets_id}")
        except Exception as e:
            print(f"Error accessing spreadsheet: {e}")
            print(f"Error type: {type(e).__name__}")
            if hasattr(e, 'response'):
                print(f"Response status: {e.response.status_code}")
                print(f"Response text: {e.response.text}")
            raise
    
    def _get_or_create_worksheet(self, sheet_name: str):
        """Get existing worksheet or create a new one."""
        try:
            return self.spreadsheet.worksheet(sheet_name)
        except gspread.WorksheetNotFound:
            # Create new worksheet
            return self.spreadsheet.add_worksheet(title=sheet_name, rows=1000, cols=20)
    
    def _get_all_account_emails(self, balances: List[AccountBalance]) -> List[str]:
        """
        Get all unique account emails from balances, sorted alphabetically.

        Args:
            balances: List of AccountBalance objects

        Returns:
            Sorted list of unique email addresses
        """
        emails = set()
        for balance in balances:
            emails.add(balance.email)
        return sorted(list(emails))

    def _find_next_available_column(self, worksheet) -> Tuple[int, str]:
        """
        Find the next available column for adding today's balance data.

        Args:
            worksheet: Google Sheets worksheet object

        Returns:
            Tuple of (column_index, column_letter) for the next available column
        """
        try:
            # Get the first row to check existing headers
            first_row = worksheet.row_values(1)

            # Find the first empty column after "Account"
            next_col_index = len(first_row) + 1

            # Convert to column letter (A=1, B=2, etc.)
            column_letter = gspread.utils.rowcol_to_a1(1, next_col_index)[:-1]

            return next_col_index, column_letter

        except Exception as e:
            print(f"Error finding next available column: {e}")
            # Default to column B if there's an error
            return 2, 'B'

    def _setup_sheet_structure(self, worksheet, all_emails: List[str]) -> None:
        """
        Setup the basic sheet structure with Account column and email addresses.

        Args:
            worksheet: Google Sheets worksheet object
            all_emails: List of all account email addresses
        """
        try:
            # Check if the sheet is empty or needs setup
            first_row = worksheet.row_values(1)

            if not first_row or first_row[0] != "Account":
                # Setup the basic structure
                print("Setting up sheet structure...")

                # Clear the sheet first
                worksheet.clear()

                # Set up the Account column
                data_to_update = [["Account"]]
                for email in all_emails:
                    data_to_update.append([email])

                # Update the sheet with account emails
                worksheet.update('A1', data_to_update)

                # Format the header
                worksheet.format('A1', {'textFormat': {'bold': True}})

                print(f"Sheet structure setup complete with {len(all_emails)} accounts")
            else:
                # Check if we need to add any new emails
                existing_emails = worksheet.col_values(1)[1:]  # Skip header
                new_emails = [email for email in all_emails if email not in existing_emails]

                if new_emails:
                    print(f"Adding {len(new_emails)} new accounts to sheet...")
                    start_row = len(existing_emails) + 2  # +1 for header, +1 for next row

                    new_data = [[email] for email in new_emails]
                    worksheet.update(f'A{start_row}', new_data)

        except Exception as e:
            print(f"Error setting up sheet structure: {e}")
            raise
    
    def update_balances(self, balances: List[AccountBalance], summary_stats: Dict[str, Any]) -> bool:
        """
        Update Google Sheets with current balance data using daily column format.

        Args:
            balances: List of AccountBalance objects
            summary_stats: Summary statistics dictionary

        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Get or create the worksheet
            worksheet = self._get_or_create_worksheet(self.config.sheet_name)

            # Get all unique account emails
            all_emails = self._get_all_account_emails(balances)

            # Setup the basic sheet structure with account emails
            self._setup_sheet_structure(worksheet, all_emails)

            # Find the next available column for today's data
            next_col_index, column_letter = self._find_next_available_column(worksheet)

            # Get today's date for the header
            today = datetime.now().strftime(self.config.date_format)

            # Check if today's column already exists
            first_row = worksheet.row_values(1)
            if len(first_row) >= next_col_index and first_row[next_col_index - 1] == today:
                print(f"Column for {today} already exists, updating existing data...")
            else:
                # Add today's date as header
                worksheet.update(f'{column_letter}1', today)
                # Format the header
                worksheet.format(f'{column_letter}1', {'textFormat': {'bold': True}})
                print(f"Added new column for {today} at column {column_letter}")

            # Create a mapping of email to total USDT balance
            balance_map = {}
            for balance in balances:
                balance_map[balance.email] = float(balance.total_usdt)

            # Get current account emails from the sheet
            current_emails = worksheet.col_values(1)[1:]  # Skip header

            # Prepare balance data for the current column
            balance_data = []
            for email in current_emails:
                usdt_balance = balance_map.get(email, 0.0)
                balance_data.append([usdt_balance])

            # Update the balance column
            if balance_data:
                start_cell = f'{column_letter}2'  # Start from row 2 (skip header)
                worksheet.update(start_cell, balance_data)

                # Format the balance column as currency
                currency_format = {'numberFormat': {'type': 'CURRENCY', 'pattern': '$#,##0.00'}}
                end_row = len(balance_data) + 1
                worksheet.format(f'{column_letter}2:{column_letter}{end_row}', currency_format)

            print(f"Successfully updated Google Sheets with {len(balance_data)} balance entries for {today}")
            print(f"Sheet ID: {self.config.google_sheets_id}")
            return True

        except Exception as e:
            print(f"Error updating Google Sheets: {e}")
            print(f"Error type: {type(e).__name__}")
            if hasattr(e, 'response'):
                print(f"Response status: {e.response.status_code}")
                print(f"Response text: {e.response.text}")
            return False
    
    def create_historical_snapshot(self, balances: List[AccountBalance], summary_stats: Dict[str, Any]) -> bool:
        """
        Create a historical snapshot in a separate worksheet with detailed breakdown.

        Args:
            balances: List of AccountBalance objects
            summary_stats: Summary statistics dictionary

        Returns:
            True if snapshot was created successfully, False otherwise
        """
        try:
            # Create worksheet name with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            sheet_name = f"Snapshot_{timestamp}"

            # Create new worksheet
            worksheet = self.spreadsheet.add_worksheet(title=sheet_name, rows=1000, cols=20)

            # Prepare detailed snapshot data
            data = self._prepare_detailed_snapshot_data(balances, summary_stats)
            worksheet.update('A1', data)

            # Format the header row
            worksheet.format('A1:J1', {'textFormat': {'bold': True}})

            # Format currency columns
            currency_format = {'numberFormat': {'type': 'CURRENCY', 'pattern': '$#,##0.00'}}
            worksheet.format('D:D', currency_format)  # Spot USDT
            worksheet.format('F:F', currency_format)  # Margin USDT
            worksheet.format('H:H', currency_format)  # Futures USDT
            worksheet.format('J:J', currency_format)  # Total USDT

            print(f"Created historical snapshot: {sheet_name}")
            return True

        except Exception as e:
            print(f"Error creating historical snapshot: {e}")
            return False

    def _prepare_detailed_snapshot_data(self, balances: List[AccountBalance], summary_stats: Dict[str, Any]) -> List[List[str]]:
        """
        Prepare detailed balance data for snapshot format.

        Args:
            balances: List of AccountBalance objects
            summary_stats: Summary statistics dictionary

        Returns:
            List of rows for Google Sheets snapshot
        """
        # Header row
        headers = [
            "Email",
            "Type",
            "Spot BTC",
            "Spot USDT",
            "Margin BTC",
            "Margin USDT",
            "Futures BTC",
            "Futures USDT",
            "Total BTC",
            "Total USDT"
        ]

        rows = [headers]

        # Data rows
        for balance in balances:
            # Only show accounts with balances or if filtering is enabled
            filter_emails = set(self.config.emails) if self.config.emails else None
            show_account = filter_emails or balance.total_btc > 0

            if show_account:
                row = [
                    balance.email,
                    balance.account_type,
                    str(balance.spot_btc),
                    str(balance.spot_usdt),
                    str(balance.margin_btc),
                    str(balance.margin_usdt),
                    str(balance.futures_btc),
                    str(balance.futures_usdt),
                    str(balance.total_btc),
                    str(balance.total_usdt)
                ]
                rows.append(row)

        # Add summary rows
        rows.append([])  # Empty row
        rows.append(["SUMMARY"])
        rows.append(["Total Accounts", str(summary_stats["total_accounts"])])
        rows.append(["Regular Accounts", str(summary_stats["regular_accounts"])])
        rows.append(["Managed Accounts", str(summary_stats["managed_accounts"])])
        rows.append(["Grand Total BTC", str(summary_stats["grand_total_btc"])])
        rows.append(["Grand Total USDT", str(summary_stats["grand_total_usdt"])])
        rows.append(["BTC Price", f"${summary_stats['btc_price']:,}"])
        rows.append(["Last Updated", summary_stats["timestamp"]])

        return rows
