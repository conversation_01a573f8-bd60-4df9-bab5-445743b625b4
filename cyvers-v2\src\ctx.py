from typing import Optional, List, Any, Dict
from asyncio import Task
from contextvars import Con<PERSON><PERSON><PERSON>
from random import randint

from .dto import Cy<PERSON><PERSON>lert
from .cfg import Configuration
from .proxy import CyversProxy


class Context:
    def __init__(self):
        self.tid: ContextVar[Optional[int]] = ContextVar("tid", default=None)
        self.task_dump: List[Task] = []
        self.schedules: List[Task] = []
        self.run: bool = True
        self.cfg: Optional[Configuration] = None
        self.alerts: List[CyversAlert] = []
        self.proxy: Optional[CyversProxy] = None

    def tid_bump(self) -> int:
        r = randint(100000, 999999)
        self.tid.set(r)
        return r

    @property
    def tid_value(self) -> int:
        return self.tid.get()


ctx: Context = Context()
