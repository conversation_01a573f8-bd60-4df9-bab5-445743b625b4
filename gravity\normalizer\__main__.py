import os.path
from gravity.normalizer.normalize_binance import BinanceNormalizer
from gravity.normalizer.normalize_binance_wrapper import BinanceGravity<PERSON>ormalizer, BinanceOTCNormalizer, BinanceKoreaProxyNormalizer
from gravity.normalizer.normalize_binanceus import BinanceUSNormalizer
from gravity.normalizer.normalize_bitbank import BitbankNormalizer
from gravity.normalizer.normalize_bitfinex import BitfinexNormalizer
from gravity.normalizer.normalize_bithumb import BithumbNormalizer
from gravity.normalizer.normalize_bitkub import BitkubNormalizer
from gravity.normalizer.normalize_bitopro import BitoproNormalizer
from gravity.normalizer.normalize_bitso import BitsoNormalizer
from gravity.normalizer.normalize_bitstamp import BitstampNormalizer
from gravity.normalizer.normalize_bitvavo import BitvavoNormalizer
from gravity.normalizer.normalize_btcturk import BtcturkNormalizer
from gravity.normalizer.normalize_coinbase import CoinbaseNormalizer
from gravity.normalizer.normalize_cryptocom import CryptocomNormalizer
from gravity.normalizer.normalize_gateio import GateioNormalizer
from gravity.normalizer.normalize_indodax import IndodaxNormalizer
from gravity.normalizer.normalize_indodax_idr import IndodaxIdrNormalizer
from gravity.normalizer.normalize_ireserve import IreserveNormalizer
from gravity.normalizer.normalize_kraken import KrakenNormalizer
from gravity.normalizer.normalize_maicoin import MaicoinNormalizer
from gravity.normalizer.normalize_upbit import UpbitNormalizer
from gravity.normalizer.normalize_valr import ValrNormalizer
from gravity.normalizer.normalize_bitget import BitgetNormalizer
from gravity.normalizer.normalize_bybit import BybitNormalizer
from gravity.normalizer.normalizer_coinone import CoinoneNormalizer
from gravity.normalizer.normalize_gatemt import GateMtNormalizer
from gravity.normalizer.normalize_coinsph import CoinsphNormalizer
from gravity.normalizer.normalize_kucoin import KucoinNormalizer
from gravity.normalizer.normalize_mercado import MercadoNormalizer
from gravity.normalizer.normalize_okcoin import OkcoinNormalizer
from gravity.normalizer.normalize_okx import OkxNormalizer
from gravity.normalizer.normalize_tokocrypto import TokocryptoNormalizer
from gravity.normalizer.normalize_tokocrypto_idr import TokocryptoIDRNormalizer

import argparse



exchange_normalizing_map = {
    "binance": BinanceNormalizer, # working
    "binance_gravity": BinanceGravityNormalizer,
    "binanceus": BinanceUSNormalizer, # working
    "binance_otc": BinanceOTCNormalizer,
    "binance_korea_proxy": BinanceKoreaProxyNormalizer,
    "bitbank": BitbankNormalizer,
    "bitfinex": BitfinexNormalizer,
    "bitget": BitgetNormalizer,
    "bithumb": BithumbNormalizer,
    "bitkub": BitkubNormalizer,
    "bitopro": BitoproNormalizer,
    "bitso": BitsoNormalizer,
    "bitstamp": BitstampNormalizer,
    "bitvavo": BitvavoNormalizer,
    "btcturk_hft": BtcturkNormalizer,
    "bybit": BybitNormalizer,
    "coinbase": CoinbaseNormalizer,
    "coinone": CoinoneNormalizer,
    "coinsph": CoinsphNormalizer,
    "cryptocom": CryptocomNormalizer,
    "gateio": GateioNormalizer,
    "gatemt": GateMtNormalizer,
    "indodax": IndodaxNormalizer,
    "indodax_idr": IndodaxIdrNormalizer,
    "ireserve": IreserveNormalizer,
    "kraken": KrakenNormalizer,
    "kucoin": KucoinNormalizer,
    "maicoin": MaicoinNormalizer,
    "mercado": MercadoNormalizer,
    "okcoinjp": OkcoinNormalizer,
    "okx": OkxNormalizer,
    "tokocrypto": TokocryptoNormalizer,
    "tokocrypto_idr": TokocryptoIDRNormalizer,
    "upbit": UpbitNormalizer,
    "valr": ValrNormalizer,
}

if __name__ == "__main__":
    parser = argparse.ArgumentParser("Normalize exported transfer files from exchanges to a unified format")

    # parser.add_argument("exchange", help="Exchange name")

    parser.add_argument("-i",  "--input-path", help="Path to directory with exported files")
    parser.add_argument("-o", "--output", help="Path to output file")
    parser.add_argument("-t", "--track-assets", help="Outputs a list of assets that were found in the input files")
    parser.add_argument("-e", "--exchange", help="Normalizes only one given exchange")
    # TODO: add support tack asset feature
    args = parser.parse_args()
    for dir in os.listdir(args.input_path):
        if args.exchange and args.exchange != dir:
            continue

        full_path = os.path.join(args.input_path, dir)
        if os.path.isdir(full_path):
            if dir in exchange_normalizing_map:
                print(f"Normalizing {dir} | {full_path}")

                if not os.path.isdir(args.output):
                    os.mkdir(args.output)

                normalizer = exchange_normalizing_map[dir]()
                normalizer.normalize_dir(args.output, full_path)
            else:
                print(f"{dir} not in normalized map, skipping")
