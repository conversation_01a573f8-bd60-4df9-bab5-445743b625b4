#!/usr/bin/env python3
import sys
import argparse
import logging
from pathlib import Path

from .analyzer import run_complete_analysis
from .db import load_config

def parse_arguments():
    parser = argparse.ArgumentParser(
        description="POSCON",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config.local.yml",
        help="Path to configuration file (default: config.local.yml)"
    )
    
    parser.add_argument(
        "--threshold", "-t",
        type=float,
        help="Threshold for flagging exchanges (overrides config)"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        help="Output Excel file path (overrides config)"
    )
    
    return parser.parse_args()


def validate_config(config_path: str) -> bool:
    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        print("Please create a configuration file based on config.example.yml")
        return False
    
    try:
        config = load_config(config_path)
        
        required_sections = ['input_csv_path', 'database']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            print(f"⚠️  Missing configuration sections: {missing_sections}")
            print("Some functionality may not work properly.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False


def print_analysis_summary(results: dict):
    print(f"\n{'='*80}")
    print("ANALYSIS RESULTS SUMMARY")
    print(f"{'='*80}")
    
    print(f"📊 Threshold Used: ${results.get('threshold', 'N/A'):,.0f}")
    print(f"🔍 Problematic Exchanges Found: {len(results.get('problematic_exchanges', []))}")
    
    if results.get('problematic_exchanges'):
        print(f"\n🚨 Exchanges Over Threshold:")
        for i, exchange in enumerate(results['problematic_exchanges'], 1):
            flagged_assets = len(results.get('flagged_assets', {}).get(exchange, []))
            print(f"   {i:2d}. {exchange:<20} ({flagged_assets} flagged assets)")
    
    if results.get('explanations'):
        total_explanations = sum(len(assets) for assets in results['explanations'].values())
        total_adjustments = 0.0
        
        for exchange_explanations in results['explanations'].values():
            for explanation in exchange_explanations.values():
                total_adjustments += explanation.get('total_adjustment_usd', 0.0)
        
        print(f"\n💡 Systematic Explanations:")
        print(f"   Assets Analyzed: {total_explanations}")
        print(f"   Total Adjustments: ${total_adjustments:,.2f}")
    
    if results.get('excel_report_path'):
        print(f"\n📋 Excel Report Generated:")
        print(f"   File: {results['excel_report_path']}")
        
        exchange_details = results.get('exchange_details', {})
        sheet_count = 2
        
        for exchange, analysis_df in exchange_details.items():
            if not analysis_df.empty:
                date_columns = [col for col in analysis_df.columns 
                              if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
                sheet_count += len(date_columns)
        
        print(f"   Sheets: {sheet_count} (2 summary + {sheet_count-2} exchange details)")


def main():
    try:
        args = parse_arguments()
        
        if not validate_config(args.config):
            return 1
        
        config = load_config(args.config)
        
        print(f"📁 Configuration: {args.config}")
        print(f"📊 Data Source: {config.get('input_csv_path', 'Not specified')}")
        print(f"🗃️  Database: {'Configured' if config.get('database') else 'Not configured'}")
        print(f"💰 Threshold: ${args.threshold or config.get('processing', {}).get('threshold', 10000):,.0f}")
        
        output_path = args.output or config.get('output_excel_path', 'output/poscon_report.xlsx')
        print(f"📋 Excel Output: {output_path}")
        
        print(f"\n{'='*80}")
        print("STARTING POSCON ANALYSIS")
        print(f"{'='*80}")
        
        if args.output:
            config['output_excel_path'] = args.output
        
        results = run_complete_analysis(
            config_path=args.config,
            threshold=args.threshold,
            generate_excel=True
        )
        
        print_analysis_summary(results)
        
        print(f"\n{'='*80}")
        print("✅ POSCON ANALYSIS COMPLETED SUCCESSFULLY")
        print(f"{'='*80}")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️  Analysis interrupted by user")
        return 130
    
    except Exception as e:
        print(f"\n❌ Error during POSCON analysis: {e}")
        logging.getLogger(__name__).error(f"POSCON analysis failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
