from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals
import os

# 加值 - deposit
# 提領 - withdraw

COLUMNS_NO_TAG = ["date", "date_raw", "type", "amount", "fee", "total", "status", "address", "txid", "_1", "_2", "internal_id"]
COLUMNS_W_TAG = ["date", "date_raw", "type", "amount", "fee", "total", "status", "address", "txid", "tag", "_1", "_2", "internal_id"]

UTC_SHIFT = 5 * 60 * 60

class BitoproNormalizer(Normalizer):
    exchange = "bitopro"

    def __init__(self):
        super().__init__(   
            _columns = COLUMNS_NO_TAG
        )

    def _file_rows(self, file_name: str):
        if "xrp" in file_name:
            self.columns = COLUMNS_W_TAG
        else:
            self.columns = COLUMNS_NO_TAG
            
        return super()._file_rows(file_name)

    def normalize_row(self, row, file_name):
        time_format = "%Y-%m-%d %H:%M:%S"
        fname = os.path.basename(file_name)
        coin = fname.split("_")[1]
        amount = row["amount"]
        ts = float(datetime.strptime(row["date"], time_format).timestamp()) - UTC_SHIFT

        if row["status"] not in ("完成"):
            print(f"{self.exchange} skipping row {coin} {amount}")
            return
        if "加值" in row["type"]:
            return Deposits(
                asset=coin.upper(),
                amount=float(amount),
                ts=ts,
                exchange=self.exchange,
                fee=float(row["fee"]),
                address=row["address"],
                tag=row.get("tag"),
                txid=row['txid'],
            )
        elif "提領" in row['type']:
            return Withdrawals(
                asset=coin.upper(),
                amount=float(amount),
                ts=ts,
                exchange=self.exchange,
                fee=float(row["fee"]),
                address=row["address"],
                tag=row.get("tag"),
                txid=row['txid'],
            )