import argparse
import asyncio
import time
from datetime import datetime

from prometheus_client import start_http_server

from .slack import Slack
from .main import sentry_mode, send_to_slack
from ._rebate import Rebate
from .config import load_config, log


def argparser():
    parser = argparse.ArgumentParser(description="Rebate calculator")
    parser.add_argument("--config", type=str, default="config.yaml", help="Config file")
    parser.add_argument("--output", type=str, required=True, help="Output dir where rebate and top trades will be written, generated output is in output dir")
    parser.add_argument("--start-time", type=int, help="Start time in timestamp")
    parser.add_argument("--end-time", type=int, help="End time in timestamp")
    parser.add_argument("--sentry-mode", "-s", action="store_true", help="Sentry mode, generate report once a month and sends to slack, generated output is in output dir and slack messages is sent")
    parser.add_argument("--bind-port", type=int, default=9090, help="Ports used by promethium")
    parser.add_argument("--bind-host", type=str, default="0.0.0.0", help="Host used by promethium")
    parser.add_argument("--send-slack", "-a", action="store_true", help="Send generated report to slack, files are sent to slack as well")
    args = parser.parse_args()
    if args.sentry_mode:
        log.info("Sentry mode is on. start-time and end-time are not used")
    return args


if __name__ == "__main__":
    args = argparser()
    config = load_config(args.config)
    loop = asyncio.get_event_loop()
    if args.sentry_mode:
        start_http_server(args.bind_port, args.bind_host)
        loop.run_until_complete(sentry_mode(config, args.output, loop))
    else:
        rebate = Rebate(loop)
        loop.run_until_complete(rebate.async_init(config))
        loop.run_until_complete(asyncio.sleep(3))
        start_time = time.time()
        start_date = datetime.fromtimestamp(start_time)
        end_date = datetime.fromtimestamp(args.end_time)
        log.info("Generating rebate report for ", args.start_time, " to ", args.end_time)
        if args.send_slack:
            slack = Slack(config["slack"]["token"], config["slack"]["channel_id"])
            slack.send_message("Rebate report is generating for " + start_date.strftime("%Y-%m-%d") + " to " + end_date.strftime("%Y-%m-%d"))
        loop.run_until_complete(rebate.process_trades(args.start_time, args.end_time))
        files = rebate.write_rebate(args.output)
        end_time = time.time()
        log.info("It took ", end_time - start_time, " to gen this report")
        if args.send_slack:
            send_to_slack(config, files, start_date, end_date)