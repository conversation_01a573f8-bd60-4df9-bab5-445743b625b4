import sys, time, logging, json
import datetime
from typing import List, Dict

from selenium.webdriver import Firefox
from prometheus_client import Gauge, Counter

log = logging.getLogger("bybit_tr_clicker")
log.addHandler(logging.StreamHandler(sys.stdout))

from .common import TransactionInfoDao, Transaction, EXCHANGE_MAPPING, main_app, ClickerBase
from .binance_turkey_login import BinanceTrAutoLogin

DEPOSIT_URL = f"https://www.binance.tr/en/usercenter/wallet/money-log/deposit"
RAW_DEPOSIT_HISTORY = "https://www.binance.tr/v1/deposits?limit=10&startTime={start_time}&endTime={end_time}"
RAW_TRAVEL_RULE_SUBMIT = "https://www.binance.tr/v1/travel-rule/submit/data"
SOURCE = 500


class BinanceTurkeyClicker(ClickerBase):

    def __init__(self, driver: Firefox, trx_dao: TransactionInfoDao, config: Dict):
        ns = "gt_clicker"
        super().__init__(driver, "binance.tr", DEPOSIT_URL, ns, log)
        self.trx_dao: TransactionInfoDao = trx_dao
        self.auto_login = BinanceTrAutoLogin(self.driver)

        self._mon_deposit_details_req = Gauge("deposit_details_req", documentation="", namespace=ns)
        self._mon_deposit_details_provided = Counter("deposit_details_provided", documentation="", namespace=ns)

        self.username = config["credentials"]["username"]
        self.password = config["credentials"]["password"]
        self.topt = config["credentials"]["secret"]

    def check_login_status(self):
        try:
            super().check_login_status()
        except:
            log.debug("System not logged in, trying to log in")
            self.auto_login.login(self.username, self.password, self.topt)

        super().check_login_status()

    def is_logged_in(self) -> bool:
        return not "signin" in self.driver.current_url

    def get_pending_deposits(self) -> List[Transaction]:
        to_ts = int(time.time() * 1000)
        from_ts = to_ts - 7 * 24 * 3600 * 1000
        log.info(f"Fetching deposits from {from_ts} to {to_ts}")
        response = self.driver.execute_script(
            """
            var xmlHttp = new XMLHttpRequest();
            xmlHttp.open("GET", arguments[0], false );
            xmlHttp.send(null);
            return xmlHttp.responseText;
            """,
            RAW_DEPOSIT_HISTORY.format(start_time=from_ts, end_time=to_ts),
        )
        response = json.loads(response)
        log.debug(f"Deposit GET response: {response}")
        deposits = []
        for entry in response["data"]["list"]:
            if entry["status"] != 18:
                continue
            deposits.append(
                Transaction(
                    asset=entry["asset"],
                    trx_id=str(entry["id"]),
                    ts=datetime.datetime.fromtimestamp(float(entry["insertTime"]) / 1000),
                    source=SOURCE,
                    amount=None,
                    txid=None,
                    address=None,
                    fee=None,
                )
            )
        self._mon_deposit_details_req.set(len(deposits))
        return deposits

    def resolve_pending(self, trx_id, source_exchange):
        post_json = {
            "channel": 2,
            "targetType": 1,
            "vasp": EXCHANGE_MAPPING[source_exchange],
            "transactionType": "1",
            "transactionId": trx_id,
            "description": "Internal transfer to another exchange wallet",
        }
        post_json = json.dumps(post_json)
        log.info(f"Posting to travel rule: {post_json}")
        response = self.driver.execute_script(
            """
            var xmlHttp = new XMLHttpRequest();
            xmlHttp.open("POST", arguments[0], false );
            xmlHttp.setRequestHeader("Content-Type", "application/json; charset=utf-8")
            xmlHttp.send(arguments[1]);
            return xmlHttp.responseText;
            """,
            RAW_TRAVEL_RULE_SUBMIT,
            post_json,
        )
        log.info(f"Resolve response: {response}")
        self._mon_deposit_details_provided.inc()

    def process_deposits(self):
        deposits = self.get_pending_deposits()
        provided_details = 0
        for dep in deposits:
            log.info(f"Processing deposit: {dep}")
            source_ex = self.trx_dao.get_deposit_source_exchange(dep)
            if not source_ex:
                log.info("Not our deposit")
            else:
                log.info(f"Source exchange: {source_ex}")
                self.resolve_pending(dep.trx_id, source_ex)
                provided_details += 1


def main():
    main_app(BinanceTurkeyClicker)


if __name__ == "__main__":
    main()
