import csv
import dataclasses
from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class MaicoinNormalizer(Normalizer):
    exchange = "maicoin"

    def normalize_row(self, row, file_name):
        # 2022-10-20T17:04:22.893721
        time_format = "%Y-%m-%dT%H:%M:%S.%f"
        ts = datetime.strptime(row["created_at"], time_format).timestamp()

        asset = row["currency"].split("-")[1]  if len(row["currency"].split("-")) > 1 else row["currency"]
        amount = float(row["amount"])
        txid = row["txid"]

        if "deposit" in file_name:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=txid,
            )
        elif "withdraw" in file_name:
            addr, tag = self.extract_addr_tags(row["to_address"])
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=addr,
                tag=tag,
                txid=txid,
            )