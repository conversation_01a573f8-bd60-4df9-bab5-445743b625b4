import time, logging, sys
import pyotp
from random import randrange
from base64 import decodebytes
from typing import Optional

from selenium.webdriver import Firefox
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

from .captcha_solver import PuzzleCaptchaSolver


LOGIN_PAGE = "https://www.binance.tr/en/account/signin"
log = logging.getLogger("binance_tr_login")
log.addHandler(logging.StreamHandler(sys.stdout))


class BinanceTrAutoLogin:

    def __init__(self, driver: Firefox):
        self.driver = driver

    def sleep(self, sleep_for, comment: Optional[str] = None):
        log.debug(f"sleeping for {sleep_for} ({comment})")
        time.sleep(sleep_for)

    def login(self, username: str, password: str, secret: str):
        self.driver.get(LOGIN_PAGE)
        self.sleep(2, "Login page load")
        log.info("Logging into exchange")
        self.driver.find_element(By.XPATH, "//form//input[@type='text']").send_keys(username)
        self.driver.find_element(By.XPATH, "//form//input[@type='password']").send_keys(password)
        self.driver.find_element(By.XPATH, "//form//button[normalize-space() = 'Sign in']").click()
        self.sleep(2, "Captach load")

        for _ in range(5):
            self.clear_captcha_error()
            self.solve_captcha()
            self.sleep(2, "Check captcha result")
            if self.requires_captcha():
                log.info("failed to solve captcha")
            else:
                break
        self.enter_topt(secret)

    def shaky_drag_slider(self, slider, position):
        log.info(f"Pulling slider to {position}")
        action = ActionChains(self.driver, 100)
        action.click_and_hold(slider)

        MOVE_CNT = 5.0
        avg_step = position / MOVE_CNT
        pos_rng = lambda: randrange(int(avg_step - avg_step / 3.0), int(avg_step + avg_step / 3.0), 1)
        moves = []
        for i in range(10):
            moves.append(pos_rng())
            if sum(moves) > position:
                break

        if sum(moves) > position + 5:
            moves[-1] = position - sum(moves[:-1]) + 4
        moves.append(position - sum(moves))
        log.info(f"Planned moves: {moves}")
        sleep_rng = lambda: randrange(int(500 / MOVE_CNT), int(1200 / MOVE_CNT), 1)
        for m in moves:
            action.move_by_offset(m, 0)
            action.pause(sleep_rng() / 1000.0)
        action.release()
        action.perform()

    def solve_captcha(self):
        bg = self.driver.find_element(By.CLASS_NAME, "geetest_canvas_bg")
        slice = self.driver.find_element(By.CLASS_NAME, "geetest_canvas_slice")
        slider = self.driver.find_element(By.CLASS_NAME, "geetest_slider_button")

        bg_screenshot = decodebytes(
            self.driver.execute_script("return arguments[0].toDataURL('image/png').substring(21);", bg).encode("utf-8")
        )
        slice_screenshot = decodebytes(
            self.driver.execute_script("return arguments[0].toDataURL('image/png').substring(21);", slice).encode(
                "utf-8"
            )
        )
        solver = PuzzleCaptchaSolver(bg_screenshot, slice_screenshot)
        position = solver.discern()
        self.shaky_drag_slider(slider, position)

    def clear_captcha_error(self):
        try:
            error_elem = self.driver.find_element(By.CLASS_NAME, "geetest_panel_error_content")
            if error_elem.is_displayed():
                error_elem.click()
                self.sleep(2, "Waiting for captcha reload")
        except:
            pass

    def requires_captcha(self):
        try:
            return self.driver.find_element(By.CLASS_NAME, "geetest_panel_box").is_displayed()
        except Exception:
            return False

    def enter_topt(self, secret):
        log.info("Entering TOPT")
        topt = pyotp.TOTP(secret).now()
        self.driver.find_element(By.XPATH, "//div[contains(@class, 'auth-box-2fa')]//input[@maxlength=6]").send_keys(
            topt
        )
        self.driver.find_element(
            By.XPATH, "//div[contains(@class, 'auth-box-2fa')]//button[contains(@class,'submit')]"
        ).click()
        self.sleep(5, "wait for login")


if __name__ == "__main__":
    firefox_service = FirefoxService(
        service_args=[
            "--connect-existing",
            "--log",
            "debug",
            "--marionette-host",
            "localhost",
            "--marionette-port",
            "2829",
        ]
    )

    ff = Firefox(service=firefox_service)
    login = BinanceTrAutoLogin(ff)

    username = "<EMAIL>"
    password = "Asd231ASDdd@"
    # login.login(username, password)
    secret = "asdfr"

    login.enter_topt(secret)
