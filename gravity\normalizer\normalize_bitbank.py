from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

asset_map = {
    "BCC": "BCH"
}

class BitbankNormalizer(Normalizer):
    exchange = "bitbank"

    def normalize_row(self, row,  file_name: str):
        asset_raw = row["asset"].upper()
        asset = asset_map.get(asset_raw, asset_raw)
        if "deposits" in file_name:
            return Deposits(
                asset=asset,
                amount=float(row['amount']),
                ts=float(row["requested_ts"]),
                exchange=self.exchange,
                fee=None,
                address=row["address"],
                tag=row.get("memo"),
                txid=row["txid"],
            )
        elif "withdrawals" in file_name:
            return Withdrawals(
                asset=asset,
                amount=float(row['amount']),
                ts=float(row["requested_ts"]),
                exchange=self.exchange,
                fee=None,
                address=row["address"],
                tag=row.get("memo"),
                txid=row["txid"]
            )