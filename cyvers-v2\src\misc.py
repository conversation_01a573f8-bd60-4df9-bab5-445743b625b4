import asyncio
import logging
from typing import Union, List, Callable
from asyncio import Task
from prometheus_client import Counter

from .cfg import PROM_NS
from . import ctx


ERROR_COUNTER = Counter(
    namespace=PROM_NS, name="errors", labelnames=["scope"], documentation="Encountered error counts"
)


async def async_bulk_wait(tasks: List[Task], silent: bool = False) -> bool:
    failure: bool = False
    while len(tasks) > 0:
        if not silent:
            logging.info(f"Waiting for tasks to complete:{len(tasks)}")
            for task in tasks:
                logging.debug(f"  task={task.get_name()}")
        await asyncio.sleep(1)
        for task in tasks:
            if task.done():
                tasks.remove(task)
                e = task.exception()
                if e is not None:
                    failure = True
                    try:
                        ctx.ctx.tid.set(task.get_context().get(ctx.ctx.tid))
                    except:
                        logging.error(f" Who the hell are y {type(task)} - {task} ")
                    logging.error(f'Task "{task.get_name()}" failed', exc_info=e)
    return failure


async def loop_exec(duration: Union[float, int], fn: Callable, *args, **kwargs) -> None:
    """
    Scheduler.

    Copy + pasta + trim from crypto exchanges

    :param duration: duration between invokes
    :param fn: function to be called
    :param args:
    :param kwargs:
    :return: None
    """
    ctx.ctx.tid_bump()
    logging.info("Scheduled Task execution")
    while True:
        await asyncio.sleep(duration)
        try:
            await fn(*args, **kwargs)
        except Exception as e:
            logging.error(f"Scheduled task failed", exc_info=e)
