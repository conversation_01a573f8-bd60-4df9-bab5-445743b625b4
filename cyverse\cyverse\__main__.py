import argparse
from prometheus_client import start_http_server
from . import context
from ._cyverse import Cy<PERSON>

def argument_parser():
    parser = argparse.ArgumentParser(description="Call when cyverse webhook is sent")
    parser.add_argument("--bind-port", "-b", type=int, default=9090, help="Ports used by promethium")
    parser.add_argument("--bind-host", "-B", type=str, default="0.0.0.0", help="Host used by promethium")
    parser.add_argument("--config", "-c", type=str, required=True, help="Config file with call priority list")
    parser.add_argument("--server-port", "-s", type=int, default=7090, help="Ports used by flask api for cyverse webhook and twilio callback")
    parser.add_argument("--server-host", "-h", type=str, default="0.0.0.0", help="Host used by flask api for cyverse webhook and twilio callback")
    return parser


if __name__ == "__main__":
    args = argument_parser().parse_args()
    config = context.load_config(args.config)
    start_http_server(args.bind_port, args.bind_host)
    cv = Cyverse(config["twilio_account_sid"], config["twilio_auth_token"], config["twilio_phone_number"], config["callback_url"], config["phone_numbers"], args.server_port, args.server_host)
    cv.run()