from gravity.normalizer.normalize_binance import BinanceNormalizer


class BinanceGravityNormalizer(BinanceNormalizer):
    exchange = 'binance_gravity'

class BinanceSub1Normalizer(BinanceNormalizer):
    exchange = 'binance_sub1'

class BinanceSub2Normalizer(BinanceNormalizer):
    exchange = 'binance_sub2'

class BinanceKoreaProxyNormalizer(BinanceNormalizer):
    exchange = 'binance_korea_proxy'

class BinanceLendingNormalizer(BinanceNormalizer):
    exchange = 'binance_lending'

class BinanceKoreaStashNormalizer(BinanceNormalizer):
    exchange = 'binance_korea_stash'

class BinanceKoreaNormalizer(BinanceNormalizer):
    exchange = 'binance_korea'

class BinanceOTCNormalizer(BinanceNormalizer):
    exchange = 'binance_otc'