import csv
import dataclasses

from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals
import pandas as pd

class KrakenNormalizer(Normalizer):
    exchange = "kraken"

    def normalize_row(self, row, file_name):
        if "deposit" in file_name:
            return Deposits(
                row["asset"],
                float(row["amount"]),
                float(row["ts"]),
                self.exchange,
                0,
                row["address"],
                None,
                None,
            )
        elif "Withdrawals" in file_name:
            return Withdrawals(
                row["asset"],
                float(row["amount"]),
                float(row["ts"]),
                self.exchange,
                float(row["fee"]),
                row["address"],
                None,
                None,
            )

    def normalize_file(
        self,
        file_name: str,
        csv_deposit_writer: csv.DictWriter,
        csv_withdrawal_writer: csv.DictWriter,
    ):
        if file_name.endswith("_deposits.csv") or file_name.endswith("_withdrawals.csv"):
            return
        columns = ["asset", "status", "ts", "date", "amount", "fee","address"]
        with open(file_name, "r") as csvfile:
            csvreader = csv.DictReader(csvfile, fieldnames=columns)
            for row in csvreader:
                transfer = self.normalize_row(row, file_name)
                if isinstance(transfer, Deposits):
                    csv_deposit_writer.writerow(dataclasses.asdict(transfer))
                elif isinstance(transfer, Withdrawals):
                    csv_withdrawal_writer.writerow(dataclasses.asdict(transfer))
                elif transfer is not None:
                    print(f"Unknown transfer type {type(transfer)}")
