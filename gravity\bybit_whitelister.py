import logging, sys, json, requests, time
from typing import Dict, Optional, List
from dataclasses import dataclass
from argparse import ArgumentParser
from csv import writer, DictReader


log = logging.getLogger()


@dataclass
class Address:
    remark: str
    coin: str
    chain_type: str
    address: str
    memo: str


HOST = "bybit.com"
# HOST = "bybit-tr.com"

BINANCE_TO_BYBIT_NETWORK_MAP = {
    "ARBITRUM": "ARBI",
    "AVAXC": "CAVAX",
    "ZKSYNCERA": "ZKSYNC",
    "CHZ2": "CHILIZ",
    "ZKSYNCERA": "ZKV2",
    "BB": "BOUNCEBIT",
    "ENJ": "ENJIN",
    "NIL": "NILLION",
    "APT": "APTOS",
    "LUNC": "LUNA",
    "TIA": "CELESTIA",
    "LUNA": "LUNANEW",
    "WAX": "WAXP",
    "AVAX": "XAVAX",
    "OPTIMISM": "OP",
    "DYM": "DYMEVM",
    "ZRC": "ZIRCUIT",
}

BYBIT_TO_BINANCE_NETWORK_MAP = {value: key for key, value in BINANCE_TO_BYBIT_NETWORK_MAP.items()}


class BybitRequester:

    default_headers = {
        "accept": "application/json",
        "accept-language": "en",
        "content-type": "application/json",
        "lang": "en",
        "platform": "pc",
        "priority": "u=1, i",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }

    def __init__(self, cookies: Dict[str, str]):
        self._cookies: Dict[str, str] = cookies
        self.default_headers = self.default_headers.copy()
        self._guid = cookies["_by_l_g_d"]

    def get_whitelist(self) -> List[Address]:
        PAGE_LENGTH = 100
        last_page = 20
        results = []
        page = 1
        while True:
            response = requests.get(
                f"https://www.{HOST}/x-api/v3/private/cht/asset-withdraw/address/address-list?limit={PAGE_LENGTH}&page={page}",
                headers=self.default_headers,
                cookies=self._cookies,
            )
            log.debug(f"Get whitelist for page {page} response {response.text}")
            response_data = response.json()
            if not self._is_success(response_data):
                break

            last_page = min(response_data["result"]["lastPage"], 200)
            for row in response_data["result"]["data"]:
                results.append(
                    Address(
                        remark=row["remark"],
                        coin=row["coin"],
                        chain_type=row["chainType"],
                        address=row["address"],
                        memo=row["destinationTag"],
                    )
                )

            if page == last_page:
                break

            page += 1
            time.sleep(0.1)

        return results

    def _is_success(self, response_data) -> bool:
        return response_data["ret_code"] == 0

    @staticmethod
    def _create_address_payload(addresses: List[Address]):
        return {
            "addresses": [
                {
                    "coin": addr.coin,
                    "chain_type": addr.chain_type,
                    "address": addr.address,
                    "destination_tag": addr.memo,
                    "remark": addr.remark,
                    "is_verified": True,
                }
                for addr in addresses
            ]
        }

    def _dump_response(self, response: requests.Response):
        log.debug(f"URL: {response.request.url}")
        log.debug(f"Request Headers: {response.request.headers}")
        log.debug(f"Request Body: {response.request.body}")
        log.debug(f"Response: {response.text}")

    def _get_risk_token(self, payload: Dict) -> str:
        url = f"https://www.{HOST}/x-api/user/public/risk/default-intercept"
        data = {"sence": 30038, "ext_info_str": json.dumps(payload).replace(" ", "")}
        headers = self.default_headers.copy()
        headers["content-type"] = "application/x-www-form-urlencoded"
        response = requests.post(url, headers=headers, cookies=self._cookies, data=data)
        response_data = response.json()
        if not self._is_success(response_data) or "risk_token" not in response_data["result"]:
            self._dump_response(response)
            raise Exception(f"Failed to fetch risk token: {response_data}")
        return response_data["result"]["risk_token"]

    def _verify_risk_token(self, risk_token: str, email2fa: bool):

        email_topt = None
        if email2fa:
            log.info("Sending email with 2fa")
            url = f"https://www.{HOST}/x-api/user/public/risk/send/code"
            data = {
                "risk_token": risk_token,
                "component_id": "email_verify",
            }
            response = requests.post(
                url, headers=self.default_headers, cookies=self._cookies, data=json.dumps(data).replace(" ", "")
            )
            if not self._is_success(response.json()):
                raise Exception(f"Failed to send email code: {response.json()}")

            email_topt = input("Please enter Email OTP: ")

        totp = input("Please enter TOTP: ")

        request_data = {"risk_token": risk_token, "component_list": {"google2fa": str(totp)}}
        if email_topt:
            request_data["component_list"]["email_verify"] = email_topt

        request_data = json.dumps(request_data).replace(" ", "")

        url = f"https://www.{HOST}/x-api/user/public/risk/verify"
        response = requests.post(url, headers=self.default_headers, cookies=self._cookies, data=request_data)
        response_data = response.json()
        if not self._is_success(response_data) or response_data["result"]["risk_token"] != risk_token:
            self._dump_response(response)
            raise Exception(f"Failed to verify risk token: {response_data}")

    def _submit_whitelist(self, payload: Dict, risk_token: str):
        url = f"https://www.{HOST}/x-api/v3/private/cht/asset-withdraw/address/batch-create"

        payload["risk_verified_result_token"] = risk_token
        payload["address_type"] = 0
        response = requests.post(
            url, data=json.dumps(payload).replace(" ", ""), headers=self.default_headers, cookies=self._cookies
        )
        response_data = response.json()
        if not self._is_success(response_data):
            self._dump_response(response)
            raise Exception(f"Failed to submit whitelist: {response_data}")

    def _verify_addresses(self, addresses: List[Address]):
        non_duplicate_addresses = []
        for addr in addresses:
            log.debug(f"Checking address {addr}")
            url = f"https://www.{HOST}/x-api/v3/private/cht/asset-withdraw/address/address-check"
            request_data = {"address": addr.address, "chain": addr.chain_type, "coin": addr.coin, "tag": addr.memo}
            request_data = json.dumps(request_data).replace(" ", "")
            response = requests.post(url, headers=self.default_headers, cookies=self._cookies, data=request_data)
            resp_data = response.json()
            if not self._is_success(resp_data):
                raise Exception(f"Failed to validate data: {resp_data}")
            results = resp_data["result"]

            if addr.memo and not results["isTagCorrect"]:
                raise Exception(f"Address {addr} memo is incorrect")
            if not results["isCorrect"]:
                raise Exception(f"Address {addr} is not correct")

            time.sleep(0.1)

            if results["isExisted"]:
                log.info(f"Address {addr} already exists")
                continue

            non_duplicate_addresses.append(addr)

        return non_duplicate_addresses

    def whitelist_addresses(self, addresses: List[Address], email_2fa: bool):

        addresses = self._verify_addresses(addresses)

        payload = self._create_address_payload(addresses)
        risk_token = self._get_risk_token(payload)
        log.info(f"Acquired risk token: {risk_token}")
        self._verify_risk_token(risk_token, email_2fa)
        log.info(f"Risk token verified")
        self._submit_whitelist(payload, risk_token)


def parse_arguments():
    parser = ArgumentParser(__file__)
    parser.add_argument("-c", required=True, help="File with cookie contents", dest="cookie_file")
    parser.add_argument("--dump-current-whitelist", action="store_true", default=False)
    parser.add_argument("--no-email-2fa", action="store_true", default=False)
    parser.add_argument("address_file", nargs="?")

    return parser.parse_args()


def parse_cookie_file_contents(contents: str) -> Dict[str, str]:
    cookie_values: Dict[str, str] = {}
    for line in contents.split("; "):
        if len(line):
            key, value = line.split("=", 1)
            cookie_values[key] = value.rstrip()
    return cookie_values


def main():

    args = parse_arguments()

    with open(args.cookie_file, "r") as cookie_fh:
        cookies = parse_cookie_file_contents(cookie_fh.read())

    log.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    log.addHandler(handler)

    bybit = BybitRequester(cookies)

    if args.dump_current_whitelist:
        with open(args.address_file, "w") as wl_file:
            wl = bybit.get_whitelist()
            wl_writer = writer(wl_file)
            wl_writer.writerow(("coin", "chain_type", "address", "memo", "remark"))
            for row in wl:
                wl_writer.writerow(
                    (
                        row.coin,
                        BYBIT_TO_BINANCE_NETWORK_MAP.get(row.chain_type, row.chain_type),
                        row.address,
                        row.memo,
                        row.remark,
                    )
                )
    else:
        with open(args.address_file, "r") as wl_file:
            wl_reader = DictReader(wl_file)
            addresses = []
            for row in wl_reader:
                addresses.append(
                    Address(
                        coin=row["coin"],
                        chain_type=BINANCE_TO_BYBIT_NETWORK_MAP.get(row["chain_type"], row["chain_type"]),
                        address=row["address"],
                        memo=row["memo"],
                        remark=row["remark"],
                    )
                )
            bybit.whitelist_addresses(addresses, not args.no_email_2fa)


if __name__ == "__main__":
    main()
