import asyncio
import logging
import signal

from . import ctx
from .cfg import Configuration
from .srv import Server
from .proxy import CyversProxy
from .misc import loop_exec, async_bulk_wait


def main():
    ctx.ctx.tid.set(999999)
    cfg = Configuration(app_name="cyvers")
    cfg.argparse()
    cfg.setup_logging()
    cfg.config_loader()
    ctx.ctx.cfg = cfg
    req_logger = cfg.request_logger()
    logging.info(f"Cyvers proxy setup")

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    ctx.ctx.srv = Server(req_logger)
    ctx.ctx.proxy = CyversProxy(req_logger)
    ctx.ctx.proxy.refill()

    logging.info(f"Cyvers proxy startup")
    loop.add_signal_handler(signal.SIGTERM, ctx.ctx.srv.sighandler)
    loop.run_until_complete(ctx.ctx.srv.start())

    ctx.ctx.schedules.append(
        loop.create_task(loop_exec(2, async_bulk_wait, tasks=ctx.ctx.task_dump, silent=True), name="task_gc")
    )
    ctx.ctx.schedules.append(loop.create_task(loop_exec(10, ctx.ctx.proxy.fan), name="alert_loop"))

    try:
        loop.run_until_complete(ctx.ctx.srv.keepalive())
    except KeyboardInterrupt as e:
        pass
    loop.run_until_complete(ctx.ctx.srv.stop())
    for i in ctx.ctx.schedules:
        i.cancel()
    for i in ctx.ctx.task_dump:
        i.cancel()

    ctx.ctx.proxy.flush()

    logging.info(f"Done")
