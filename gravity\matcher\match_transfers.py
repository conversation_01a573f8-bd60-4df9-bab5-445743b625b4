import collections
import csv
import os

from typing import Dict, List, Tuple, Set
from gravity.matcher._types import <PERSON><PERSON><PERSON><PERSON>
from gravity.matcher.address_book import AddressBook
from gravity.matcher.cap_withdrawals import CapWithdrawals
from gravity.normalizer.normal import Transfer
from tqdm import tqdm

exchanges = [
    "binance",
    "binanceus",
    "binance_gravity",
    "binance_otc",
    "binance_korea_proxy",
    "bitbank",
    "bitget",
    "bitfinex",
    "bithumb",
    "bitkub",
    "bitopro",
    "bitso",
    "bitstamp",
    "bitvavo",
    "btcturk_hft",
    "bybit",
    "coinbase",
    "coinone",
    "coinsph",
    "cryptocom",
    "gateio",
    "gatemt",
    "indodax",
    "indodax_idr",
    "ireserve",
    "kraken",
    "kucoin_funding",
    "maicoin",
    "mercado",
    "okcoinjp",
    "okx",
    "tokocrypto",
    "tokocrypto_idr",
    "upbit",
    "valr"
]


# Do we need this? Don't know lets get back to this later


SPECIFIC_TRANSACTION_DEBUG = ""

AMOUNT_AND_TIMESTAMP_MATCH = "AMOUNT_AND_TIMESTAMP_MATCH"
TXID_MATCH = "TXID_MATCH"
AMOUNT_MATCH = "AMOUNT_MATCH"
MERGED_DEPOSIT_MATCH = "MERGED_DEPOSIT_MATCH"

def transfers_match(withdrawal: Transfer, deposit: Transfer):
    # if "01504a309f15902959" in withdrawal.txid or "01504a309f15902959" in deposit.txid:
    #     print("====")
    #     print(f"{withdrawal.txid} | {withdrawal.exchange}")
    #     print(f"{deposit.txid} | {deposit.exchange}")

    if withdrawal.txid and deposit.txid and (withdrawal.txid.upper() == deposit.txid.upper()
        or withdrawal.txid.upper() in deposit.txid.upper()
        or deposit.txid.upper() in withdrawal.txid.upper()):
        return TXID_MATCH
    elif (
        (withdrawal.amount - withdrawal.fee) * 0.99
        <= deposit.amount
        <= (withdrawal.amount * 1.0001)
        and withdrawal.ts <= deposit.ts + 1 < (withdrawal.ts + 60 * 60 * 24 * 7)
    ):
        return AMOUNT_AND_TIMESTAMP_MATCH
    elif (
        withdrawal.amount * 0.99 <= deposit.amount
        <= withdrawal.amount
        and (withdrawal.exchange == "bithumb"
        and deposit.exchange in ("binance_korea_proxy", "upbit"))
        or (withdrawal.exchange == "upbit"
        and deposit.exchange in ("binance_korea_proxy", "bithumb"))
    ):
        return AMOUNT_MATCH
    return None


def merged_transfers_match(trx, deposit, merged_deposit):
    if trx.ts <= deposit.ts < (trx.ts + 60 * 60 * 6) \
        and trx.amount < deposit.amount \
        and not (trx.address and trx.address != deposit.address) \
        and (deposit in merged_deposit and trx.amount <= merged_deposit):
        return deposit
    
def row_txid_matches_debug_txid(tx_id) -> bool:
    return SPECIFIC_TRANSACTION_DEBUG and SPECIFIC_TRANSACTION_DEBUG in tx_id


class TransferMatcher(IOHandler):
    confirmed_cap2_deposits: Set[Tuple[str, str]]

    def __init__(
        self,
        input_path: str,
        output_path: str,
        cap_trx: CapWithdrawals,
        address_book: AddressBook,
    ):
        super().__init__(input_path, output_path)
        self.cap_trx = cap_trx
        self.address_book = address_book
        self.merged_deposit = {}
        self.confirmed_cap2_deposits = set()

    def get_all_withdrawals_for_asset(self, asset):
        withdrawals = collections.defaultdict(list)
        for exchange in exchanges:
            for l in self.get_exchange_asset_withdrawals(exchange, asset):
                withdrawals[exchange].append(l)
        return withdrawals

    def get_all_deposits_for_asset(self, asset):
        deposits = collections.defaultdict(list)
        for exchange in deposits:
            for l in self.get_exchange_asset_deposits(exchange, asset):
                deposits[exchange].append(l)
        return deposits

    def load_all_transfers_for_asset(self, input_path: str, assets: List[str] = None):
        withdrawals: Dict[str,  Dict[str,List[Transfer]]] = {ex: {} for ex in exchanges}
        deposits: Dict[str, Dict[str, List[Transfer]]] = {ex: {} for ex in exchanges}
        for exchange in exchanges:
            if not os.path.exists(os.path.join(input_path, f"{exchange}_deposits.csv")):
                print(f"skipping {exchange}, no deposit file")
                continue

            print(f"Opening {exchange}_deposits.csv")
            with open(
                os.path.join(input_path, f"{exchange}_deposits.csv")
            ) as csv_deposit_file:
                csv_deposit_reader = csv.DictReader(csv_deposit_file)
                for row in csv_deposit_reader:
                    if "Internal transfer" in row["txid"]:
                        continue
                    if row["asset"] not in deposits[exchange]:
                        deposits[exchange][row["asset"]] = []
                    if assets:
                        if row["asset"] not in assets:
                            continue
                    deposits[exchange][row["asset"]].append(Transfer.from_row(row))

                    if row_txid_matches_debug_txid(row["txid"]):
                        print(f"found deposit in {csv_deposit_file}")
                        print(row)
            with open(
                os.path.join(input_path, f"{exchange}_withdrawals.csv")
            ) as csv_withdrawal_file:
                csv_withdrawal_reader = csv.DictReader(csv_withdrawal_file)
                for row in csv_withdrawal_reader:
                    if "Internal transfer" in row["txid"]:
                        continue
                    if row["asset"] not in withdrawals[exchange]:
                        withdrawals[exchange][row["asset"]] = []
                    if assets:
                        if row["asset"] not in assets:
                            continue
                    withdrawals[exchange][row["asset"]].append(Transfer.from_row(row))
                    if row_txid_matches_debug_txid(row["txid"]):
                        print(f"found withdrawal in {csv_withdrawal_file}")
                        print(row)

        return deposits, withdrawals

    def report_matched_transfers(self, matched_transfers: List[Tuple[Transfer, Transfer, str]]):
        with open(os.path.join(self.output_path, "matched_transfers.csv"), "w", newline="") as w:
            csv_writer = csv.DictWriter(
                w, fieldnames=["deposit", "withdrawal", "match_type"]
            )

            csv_writer.writeheader()
            for withdrawal, deposit, match_type in matched_transfers:
                csv_writer.writerow(
                    {
                        "deposit": deposit,
                        "withdrawal": withdrawal,
                        "match_type": match_type,
                    }
                )

    def report_unmatched_transfers(self, exchange, unmatched_withdrawals):
        print("reporting unmatched transfers")
        confirmed_transactions_found = 0
        with open(os.path.join(self.output_path, exchange + "_unmatched_withdrawals.csv"), "w", newline="") as w:
            csv_writer = csv.DictWriter(
                w, fieldnames=Transfer.__dataclass_fields__.keys()
            )
            print("writing headers")
            csv_writer.writeheader()
            print("writing data")
            for withdrawal in unmatched_withdrawals:
                if withdrawal.txid:
                    if (withdrawal.asset, withdrawal.txid) in self.confirmed_cap2_deposits:
                        # print(f"Found confirmed transaction: {withdrawal.txid} | {withdrawal.ts}")
                        confirmed_transactions_found += 1
                        continue

                csv_writer.writerow(withdrawal.to_dict())
            print("done writing")
        # with open(os.path.join(self.output_path, "unmatched_deposits.csv"), "w") as w:
        #     csv_writer = csv.DictWriter(
        #         w, fieldnames=Transfer.__dataclass_fields__.keys()
        #     )
        #     csv_writer.writeheader()
        #     for deposit in unmatched_deposits:
        #         csv_writer.writerow(deposit.to_dict())
        print(f"Found {confirmed_transactions_found} due to capcap2 db exports")
        print("done with unmatched transfers")

    def find_deposit_exchange(self, asset, address):
        if not address:
            return
        else:
            return self.address_book.inverted_address.get(asset, {}).get(address.upper(), [])

    def add_matcing_deposit(self, matched_deposits, deposit):
        if deposit.exchange not in matched_deposits:
            matched_deposits[deposit.exchange] = {}
        if deposit.asset not in matched_deposits[deposit.exchange]:
            matched_deposits[deposit.exchange][deposit.asset] = []
        matched_deposits[deposit.exchange][deposit.asset].append(deposit)

    def if_matched_deposits(self, matched_deposits, deposit):
        if deposit.exchange in matched_deposits:
            if deposit.asset in matched_deposits[deposit.exchange]:
                return deposit in matched_deposits[deposit.exchange][deposit.asset]
        return False

    def match_transfers(
        self,
        deposits: Dict[str, Dict[str, List[Transfer]]],
        withdrawals: Dict[str, Dict[str, List[Transfer]]],
        start_timestamp = None,
        end_timestamp = None
    ):
        matched_deposits = {}
        kraken_deposits = []
        bad_exchanges = [] # list off exchanges that should be skipped
        for exchange in exchanges:
            unmatched_withdrawals = []
            if exchange in bad_exchanges:
                continue
            print(f"Match withdrawals for {exchange}, from={start_timestamp}|to={end_timestamp}")
            for asset, _transfers in tqdm(withdrawals[exchange].items()):
                for withdrawal in _transfers:

                    if start_timestamp and withdrawal.ts < start_timestamp:
                        continue

                    if end_timestamp and withdrawal.ts > end_timestamp:
                        continue

                    found_transaction = False
                    if row_txid_matches_debug_txid(withdrawal.txid):
                        print(f"WD found; ts: {withdrawal.ts}")
                        found_transaction = True
                        print(withdrawal)


                    matched = False
                    if withdrawal.address and withdrawal.address != "None":
                        deposit_exchanges =  self.find_deposit_exchange(asset, withdrawal.address)
                        withdrawal.receiver = deposit_exchanges
                        if deposit_exchanges:

                            if found_transaction:
                                print(f"Are deposit exchanges:")
                                print(deposit_exchanges)
                                print(f"Keys: {deposits.keys()}")

                            for ex in deposit_exchanges:
                                if ex == exchange or ex not in deposits:
                                    continue

                                asset_deposits = deposits[ex]
                                # print(asset_deposits)
                                _deposits = asset_deposits.get(asset, [])

                                if found_transaction:
                                    print(f"{ex} deposit amount: {len(_deposits)}")

                                for deposit in _deposits:
                                    if found_transaction and row_txid_matches_debug_txid(deposit.txid):
                                        print(f"Matching TX Id DEP found; ts: {deposit.ts}")
                                        print(deposit)

                                    they_match = transfers_match(withdrawal, deposit)
                                    if they_match:
                                        # if self.if_matched_deposits(matched_deposits, deposit):
                                        #     print("Already matched deposit", deposit)
                                        # matched_transfers.append((withdrawal, deposit, they_match))
                                        matched = True
                                        break
                                if matched:
                                    break

                            if found_transaction:
                                print(f"Matched? {matched}")

                            if len(deposit_exchanges) >= 1 and list(deposit_exchanges)[0] in (
                            "luno", "luno_ZAR", "bitfinex"):
                                continue
                        else:
                            _deposit_exchanges = [ex for ex in deposit_exchanges if ex not in bad_exchanges]

                            if found_transaction:
                                print(f"missed deposit exchanges")
                                print(_deposit_exchanges)

                            for ex in _deposit_exchanges:
                                if ex == exchange:
                                    continue
                                for deposit in deposits[ex].get(asset, {}):
                                    they_match = transfers_match(withdrawal, deposit)
                                    if they_match:
                                        # if deposit in matched_deposits:
                                        #     print("Already matched deposit", deposit)
                                        # if self.if_matched_deposits(matched_deposits, deposit):
                                        #     print("Already matched deposit", deposit)
                                        self.add_matcing_deposit(matched_deposits, deposit)
                                        # matched_transfers.append((withdrawal, deposit, they_match))
                                        matched = True
                                        break
                                if matched:
                                    break
                    else:
                        for ex in deposits:
                            if ex == exchange:
                                continue
                            for deposit in deposits[ex].get(asset, {}):
                                if transfers_match(withdrawal, deposit):
                                    # if self.if_matched_deposits(matched_deposits, deposit):
                                    #     print("Already matched deposit", deposit)
                                    self.add_matcing_deposit(matched_deposits, deposit)
                                    matched = True
                                    break
                            if matched:
                                break
                    if not matched:
                        unmatched_withdrawals.append(withdrawal)
                        continue
            merged_trx = []
            print("unmatched withdrawals")
            for trx in unmatched_withdrawals:
                resp = self.merged_transfers(trx, deposits)
                if resp:
                    merged_trx.append(trx)
                elif trx.receiver:
                    rec =  [x for x in trx.receiver if x in ("kraken", 'luno', "luno_ZAR", "bitfinex")]
                    if rec:
                        kraken_deposits.append(trx)
                        merged_trx.append(trx)

            print(f"preparing list, {len(unmatched_withdrawals )} | {len(merged_trx)}")
            prepped_list = []
            for x in tqdm(unmatched_withdrawals):
                if x not in merged_trx:
                    prepped_list.append(x)
            # prepped_list = [x for x  in unmatched_withdrawals if x not in merged_trx]
            self.report_unmatched_transfers(exchange, prepped_list)
        print("kraken transfers")
        self.kraken_transfers(kraken_deposits)
        print("done kraken transfers")

    def kraken_transfers(self, transfers: List[Transfer]):
        with open(os.path.join(self.output_path,  "missing_deposits.csv"), "w", newline="") as w:
            csv_writer = csv.DictWriter(
                w, fieldnames=Transfer.__dataclass_fields__.keys(), 
            )
            for trx in transfers:
                csv_writer.writerow(trx.to_dict())

    def merged_transfers(self, trx: Transfer, deposits: Dict) -> bool:
        for ex in deposits:
            if ex == trx.exchange and ex not in ("kraken", "coinbase"):
                for deposit in deposits[ex].get(trx.asset, {}):
                    if merged_transfers_match(trx, deposit, self.merged_deposit):
                        return True

    def load_transfers(self, assets=None):
        return self.load_all_transfers_for_asset(self.input_path, assets)

    def load_confirmed_deposits(self, path: str):
        for dir in os.listdir(path):
            full_file_path = os.path.join(path, dir)
            if full_file_path.endswith(".csv"):
                print(f"Reading confirmed deposits from {full_file_path}")
                with open(full_file_path, "r", newline='') as csv_file:
                    csv_reader = csv.DictReader(csv_file)
                    for row in csv_reader:
                        if row["txid"] is None:
                            continue
                        else:
                            asset = row["gt_asset"]
                            txid = row["txid"]
                            self.confirmed_cap2_deposits.add((asset, txid))