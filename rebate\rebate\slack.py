from slack_sdk import Web<PERSON>lient
from slack_sdk.web import SlackResponse


class Slack:
    def __init__(self, token, channel_id):
        self.token = token
        self.client = None
        self.channel_id = channel_id

    def create_client(self):
        return WebClient(token=self.token)

    def send_message(self, message: str) -> SlackResponse:
        if not self.client:
            self.client = self.create_client()
        response = self.client.chat_postMessage(
            channel=self.channel_id,
            text=message
        )
        return response

    def send_file(self, file_name: str, message: str) -> SlackResponse:
        if not self.client:
            self.client = self.create_client()
        response = self.client.files_upload(
            channels=self.channel_id,
            initial_comment=message,
            file=file_name,
        )
        return response