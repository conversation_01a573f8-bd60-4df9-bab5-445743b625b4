
# 入金 - deposit
# 出金 - withdrawal

from datetime import datetime, timezone
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class BitgetNormalizer(Normalizer):
    exchange = "bitget"

    def normalize_row(self, row, file_name):
        time_format = "%Y-%m-%d %H:%M:%S"
        timestamp = int(datetime.strptime(row["Date"], time_format).replace(tzinfo=timezone.utc).timestamp())
        asset = row['Coin']
        amount = abs(float(row['Quantity']))
        txid = row['TxID']
        fee = None

        if "Deposit" in row['Type']:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=timestamp,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=txid,
            )
        elif "Withdraw" in row['Type']:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=timestamp,
                exchange=self.exchange,
                fee=fee,
                address=None,
                tag=None,
                txid=txid
            )
        
        return None