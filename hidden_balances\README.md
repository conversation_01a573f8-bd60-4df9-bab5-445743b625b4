# Hidden Balances

Hidden Balances is a service that collects Binance sub-account balances and tracks them in Google Sheets with daily column format for historical balance tracking.

## Installation

```bash
pip install -r requirements.txt
```

## Usage

```bash
python -m balance_updater [options]
```

### Options
- `--config`: Path to configuration file (default: config.yml)
- `--console-only`: Only print to console, skip Google Sheets update
- `--no-sheets`: Skip Google Sheets update
- `--verbose, -v`: Enable verbose logging

### Examples

```bash
# Standard run with Google Sheets update
python -m balance_updater

# Console output only
python -m balance_updater --console-only

# Verbose logging
python -m balance_updater --verbose
```

## Configuration

Copy `config.template.yml` to `config.yml` and configure:

```yaml
# Binance API Configuration
api_key: "YOUR_BINANCE_API_KEY"
api_secret: "YOUR_BINANCE_API_SECRET"

# Google Sheets configuration
google_sheets_id: "YOUR_GOOGLE_SHEETS_ID"
google_credentials_path: "path/to/service-account.json"
sheet_name: "Sheet1"

# Balance tracking configuration
use_daily_columns: true
date_format: "%Y-%m-%d"
end_of_day_cutoff_hour: 6

# Email filter (optional - leave empty to process all accounts)
emails:
  - <EMAIL>
  - <EMAIL>
```

### Google Sheets Setup

1. Create a Google Cloud Project and enable Google Sheets API
2. Create a service account and download the JSON credentials
3. Share your Google Sheet with the service account email
4. Set the sheet ID in your config

### End-of-Day Logic

The `end_of_day_cutoff_hour` setting controls date assignment:
- If run before cutoff hour → Uses previous day's date (end-of-day)
- If run after cutoff hour → Uses current day's date

Example with `end_of_day_cutoff_hour: 6`:
- Run at 5:59 AM → Yesterday's date (end-of-day balances)
- Run at 6:01 AM → Today's date

## Output Format

### Console Output
Displays detailed balance breakdown by account type (spot, margin, futures) with summary statistics.

### Google Sheets Format
- Column A: Account emails
- Column B+: Daily balance columns (YYYY-MM-DD format)
- Each cell contains total USDT balance for that account on that date

## Systemd Deployment

See `systemd/` directory for service and timer configuration files for automated daily collection.

## Project Structure

```
balance_updater/
├── __main__.py              # CLI entry point
├── balance_aggregator.py    # Balance collection and aggregation
├── binance_client.py        # Binance API client
├── config.py               # Configuration management
├── price_service.py        # Price fetching service
└── sheets_updater_direct.py # Google Sheets integration
```
