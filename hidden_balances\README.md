# Binance Sub-Account Balance Collector

A module for collecting and tracking Binance sub-account balances across spot, margin, and futures accounts, with Google Sheets integration for reporting.

## Features

- Collects balances from both regular and managed sub-accounts
- Supports spot, margin, and futures account types
- Converts all balances to both BTC and USDT values
- Updates Google Sheets with current balance data
- Can create historical snapshots for tracking over time
- Configurable to filter specific sub-accounts
- Designed for scheduled execution via systemd

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd ops-tools
```

2. Create and activate a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:

```bash
pip install -r untracked_balances/requirements.txt
```

4. Create configuration file:

```bash
cp untracked_balances/config.template.yml untracked_balances/config.yml
```

5. Edit the configuration file with your Binance API credentials and other settings.

## Configuration

Edit `config.yml` with the following information:

- `api_key`: Your Binance API key
- `api_secret`: Your Binance API secret
- `google_sheets_id`: (Optional) ID of the Google Sheets document to update
- `google_credentials_path`: Path to Google service account credentials file
- `emails`: List of sub-account emails to filter (leave empty to process all)

## Google Sheets Integration

To use the Google Sheets integration:

1. Create a Google Cloud project and enable the Google Sheets API
2. Create a service account and download the credentials JSON file
3. Share your Google Sheets document with the service account email
4. Update the `google_sheets_id` and `google_credentials_path` in your config

## Usage

Run the balance collector:

```bash
python -m untracked_balances.run_balance_collector
```

Command-line options:

- `--config PATH`: Path to configuration file (default: config.yml)
- `--no-sheets`: Skip Google Sheets update
- `--snapshot`: Create a historical snapshot in Google Sheets
- `--verbose`: Enable verbose logging
- `--console-only`: Only print to console, no sheets update

## Scheduled Execution

To set up scheduled execution on a Linux server:

1. Copy the systemd service and timer files:

```bash
sudo cp untracked_balances/systemd/balance-collector.service /etc/systemd/system/
sudo cp untracked_balances/systemd/balance-collector.timer /etc/systemd/system/
```

2. Edit the service file to match your installation paths.

3. Enable and start the timer:

```bash
sudo systemctl daemon-reload
sudo systemctl enable balance-collector.timer
sudo systemctl start balance-collector.timer
```

See `untracked_balances/systemd/README.md` for more details.

## Module Structure

- `__init__.py`: Package initialization
- `config.py`: Configuration management
- `binance_client.py`: Binance API client
- `price_service.py`: Cryptocurrency price conversion
- `balance_aggregator.py`: Balance collection and aggregation
- `sheets_updater.py`: Google Sheets integration
- `main.py`: Main orchestration logic
- `run_balance_collector.py`: Standalone executable script

## Security Notes

- Store your API keys securely
- Use environment variables for sensitive information in production
- The Binance API key only needs read permissions
- Consider using a dedicated service account for Google Sheets
