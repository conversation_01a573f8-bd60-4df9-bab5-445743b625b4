import datetime

from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class TokocryptoNormalizer(Normalizer):
    exchange = "tokocrypto"

    def normalize_row(self, row, file_name):
        # 2024-03-31 16:34
        time_format = "%Y-%m-%d %H:%M"
        ts = datetime.datetime.strptime(row["Time"], time_format).timestamp()
        amount = float(str(row["Amount"]).replace(",", ""))
        asset = row["Coin"]

        info_raw = row["Information"]
        info = info_raw.split()

        address = info[0][7]
        tag = None
        txid = None

        if "memo" in info_raw:
            tag = info[1]
            txid = info[2][6:] # get rid of "txid0x"
        else:
            txid = info[1][6:]

        if "deposit" in file_name:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid,
            )
        elif "withdraw" in file_name:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid
        )