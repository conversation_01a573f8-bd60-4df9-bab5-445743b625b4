from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class CoinbaseNormalizer(Normalizer):
    exchange = "coinbase"

    def normalize_row(self, row,  file_name: str):

        ts = float(row["requested_ts"])
        asset = row["asset"]
        txid = row["txid"]
        amount = float(row["amount"])
        address = row["address"]
        memo = row["memo"]

        if "deposit" in file_name:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=memo,
                txid=txid,
            )
        elif "withdraw" in file_name:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=memo,
                txid=txid,
            )