from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class CoinsphNormalizer(Normalizer):
    exchange = "coinsph"

    def normalize_row(self, row, file_name: str):

        ts = datetime.strptime(row["insert_time"], "%b %d, %Y, %H:%M").timestamp()
        asset = row["coin"]
        txid = row["tx_id"]
        amount = float(row["amount"].replace(",", ""))

        if "deposit" in file_name:
            if row["status"] != 1 or not row["complete_time"]:
                # print(f"skipping {row}")
                return None

            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=row["target_address"],
                tag=str(row["target_address_tag"]),
                txid=txid,
            )
        elif "withdraw" in file_name:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=float(row["transaction_fee"].replace(",", "")),
                address=row["address"],
                tag=row["address_tag"],
                txid=txid,
            )