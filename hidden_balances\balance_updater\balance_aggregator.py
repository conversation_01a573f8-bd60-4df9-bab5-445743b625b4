"""
Balance aggregation service for collecting and organizing sub-account balances.
"""

import time
from decimal import Decimal, ROUND_DOWN
from typing import List, Dict, Any
from dataclasses import dataclass

from .config import Config
from .binance_client import BinanceClient
from .price_service import PriceService


@dataclass
class AccountBalance:
    """Data class for account balance information."""
    email: str
    account_type: str  # 'regular' or 'managed'
    spot_btc: Decimal
    spot_usdt: Decimal
    margin_btc: Decimal
    margin_usdt: Decimal
    futures_btc: Decimal
    futures_usdt: Decimal
    total_btc: Decimal
    total_usdt: Decimal


class BalanceAggregator:
    """Service for aggregating balances across all sub-accounts."""
    
    def __init__(self, config: Config, binance_client: BinanceClient, price_service: PriceService):
        """
        Initialize balance aggregator.
        
        Args:
            config: Configuration object
            binance_client: BinanceClient instance
            price_service: PriceService instance
        """
        self.config = config
        self.binance_client = binance_client
        self.price_service = price_service
        self.dec_places = Decimal("0.********")  # for nice rounding
    
    def _quantize(self, value: Decimal) -> Decimal:
        """Round decimal to configured precision."""
        return value.quantize(self.dec_places, rounding=ROUND_DOWN)
    
    def collect_regular_account_balances(self) -> List[AccountBalance]:
        """
        Collect balances for all regular sub-accounts.
        
        Returns:
            List of AccountBalance objects for regular accounts
        """
        results = []
        btc_usdt_price = self.price_service.get_price("BTCUSDT")
        
        # Get all regular account data
        reg_emails = self.binance_client.get_regular_sub_accounts()
        spot_btc = self.binance_client.get_regular_spot_btc_totals()
        usdtm_usd, coinm_btc = self.binance_client.get_regular_futures_summaries()
        margin_btc = self.binance_client.get_regular_margin_btc_totals()
        
        # Filter emails if configured
        filter_emails = set(self.config.emails) if self.config.emails else None
        
        for email in reg_emails:
            # Only process emails that are in the config filter list (if any)
            if not filter_emails or email in filter_emails:
                # Spot balances
                spot_btc_val = spot_btc.get(email, Decimal("0"))
                spot_usdt_val = spot_btc_val * btc_usdt_price
                
                # Futures balances
                fut_usdtm_usdt = Decimal(usdtm_usd.get(email, 0))
                fut_usdtm_btc = fut_usdtm_usdt / btc_usdt_price
                
                fut_coinm_btc = Decimal(coinm_btc.get(email, 0))
                fut_coinm_usdt = fut_coinm_btc * btc_usdt_price
                
                # Margin balances
                margin_btc_val = Decimal(margin_btc.get(email, 0))
                margin_usdt_val = margin_btc_val * btc_usdt_price
                
                # Totals
                total_btc = spot_btc_val + fut_usdtm_btc + fut_coinm_btc + margin_btc_val
                total_usdt = spot_usdt_val + fut_usdtm_usdt + fut_coinm_usdt + margin_usdt_val
                
                results.append(AccountBalance(
                    email=email,
                    account_type="regular",
                    spot_btc=self._quantize(spot_btc_val),
                    spot_usdt=self._quantize(spot_usdt_val),
                    margin_btc=self._quantize(margin_btc_val),
                    margin_usdt=self._quantize(margin_usdt_val),
                    futures_btc=self._quantize(fut_usdtm_btc + fut_coinm_btc),
                    futures_usdt=self._quantize(fut_usdtm_usdt + fut_coinm_usdt),
                    total_btc=self._quantize(total_btc),
                    total_usdt=self._quantize(total_usdt),
                ))
        
        return results
    
    def collect_managed_account_balances(self) -> List[AccountBalance]:
        """
        Collect balances for all managed sub-accounts.
        
        Returns:
            List of AccountBalance objects for managed accounts
        """
        results = []
        btc_usdt_price = self.price_service.get_price("BTCUSDT")
        
        # Get managed account emails
        man_emails = self.binance_client.get_managed_sub_accounts()
        
        # Filter emails if configured
        filter_emails = set(self.config.emails) if self.config.emails else None
        
        for email in man_emails:
            # Only process emails that are in the config filter list (if any)
            if not filter_emails or email in filter_emails:
                # Spot balances
                spot_btc_val = self.binance_client.get_managed_spot_btc_total(email)
                spot_usdt_val = spot_btc_val * btc_usdt_price
                
                # Futures balances
                fut_usdt, fut_btc = self.binance_client.get_managed_futures_totals(email, self.price_service)
                
                # Margin balances
                margin_usdt, margin_btc = self.binance_client.get_managed_margin_totals(email, self.price_service)
                
                # Totals
                total_btc = spot_btc_val + fut_btc + margin_btc
                total_usdt = spot_usdt_val + fut_usdt + margin_usdt
                
                results.append(AccountBalance(
                    email=email,
                    account_type="managed",
                    spot_btc=self._quantize(spot_btc_val),
                    spot_usdt=self._quantize(spot_usdt_val),
                    margin_btc=self._quantize(margin_btc),
                    margin_usdt=self._quantize(margin_usdt),
                    futures_btc=self._quantize(fut_btc),
                    futures_usdt=self._quantize(fut_usdt),
                    total_btc=self._quantize(total_btc),
                    total_usdt=self._quantize(total_usdt),
                ))
        
        return results
    
    def collect_all_balances(self) -> List[AccountBalance]:
        """
        Collect balances for all sub-accounts (regular and managed).
        
        Returns:
            List of all AccountBalance objects sorted by total USDT value
        """
        print("Collecting regular account balances...")
        regular_balances = self.collect_regular_account_balances()
        
        print("Collecting managed account balances...")
        managed_balances = self.collect_managed_account_balances()
        
        # Combine and sort by total USDT value (descending)
        all_balances = regular_balances + managed_balances
        all_balances.sort(key=lambda x: x.total_usdt, reverse=True)
        
        return all_balances
    
    def get_summary_stats(self, balances: List[AccountBalance]) -> Dict[str, Any]:
        """
        Generate summary statistics for the collected balances.
        
        Args:
            balances: List of AccountBalance objects
            
        Returns:
            Dictionary with summary statistics
        """
        filter_emails = set(self.config.emails) if self.config.emails else None
        
        if filter_emails:
            # When filtering, count all filtered accounts
            total_accounts = len(balances)
            regular_accounts = len([b for b in balances if b.account_type == 'regular'])
            managed_accounts = len([b for b in balances if b.account_type == 'managed'])
        else:
            # When not filtering, count only accounts with balances
            total_accounts = len([b for b in balances if b.total_btc > 0])
            regular_accounts = len([b for b in balances if b.account_type == 'regular' and b.total_btc > 0])
            managed_accounts = len([b for b in balances if b.account_type == 'managed' and b.total_btc > 0])
        
        # Calculate grand totals
        grand_total_btc = sum(b.total_btc for b in balances)
        grand_total_usdt = sum(b.total_usdt for b in balances)
        
        return {
            "total_accounts": total_accounts,
            "regular_accounts": regular_accounts,
            "managed_accounts": managed_accounts,
            "grand_total_btc": self._quantize(grand_total_btc),
            "grand_total_usdt": self._quantize(grand_total_usdt),
            "btc_price": self.price_service.get_price("BTCUSDT"),
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())
        }
