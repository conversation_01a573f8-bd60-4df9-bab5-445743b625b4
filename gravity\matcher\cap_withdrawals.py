
import csv
from dataclasses import dataclass
import os

# coin,timestamp_requested,timestamp_finished,from,to,withdrawal_amount,deposit_amount,status,from_total_balance,to_total_balance,total_balance,id,txid,address,memo,network,fee_exchange_rate,gt_id,comment
@dataclass
class CapWithdrawal:
    coin: str
    timestamp_requested: int
    timestamp_finished: int
    from_: str
    to: str
    withdrawal_amount: float
    deposit_amount: float
    status: str
    from_total_balance: float
    to_total_balance: float
    total_balance: float
    id: str
    txid: str
    address: str
    memo: str
    network: str
    fee_exchange_rate: float
    gt_id: str
    comment: str

class CapWithdrawals:
    def __init__(self, file_path: str):
        self.file_path = file_path

    def get_withdrawal_for_asset(self, asset: str):
        for files in os.listdir(self.file_path):
            with open(files) as csv_file:
                csv_reader = csv.DictReader(csv_file)
                for row in csv_reader:
                    if row["coin"] == asset:
                        yield CapWithdrawal(**row)
