"""Direct Google Sheets API integration without Drive API dependency."""

import os
import requests
import urllib.parse
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from google.oauth2.service_account import Credentials
from google.auth.transport.requests import Request

from .config import Config
from .balance_aggregator import AccountBalance


class DirectSheetsUpdater:
    """Service for updating Google Sheets using direct API calls."""

    def __init__(self, config: Config):
        self.config = config

        if not config.google_sheets_id:
            raise ValueError("Google Sheets ID not configured")

        if not config.google_credentials_path or not os.path.exists(config.google_credentials_path):
            raise ValueError(f"Google credentials file not found: {config.google_credentials_path}")

        self._init_credentials()
    
    def _init_credentials(self):
        """Initialize Google Sheets API credentials."""
        scope = ['https://www.googleapis.com/auth/spreadsheets']
        
        self.credentials = Credentials.from_service_account_file(
            self.config.google_credentials_path, 
            scopes=scope
        )
        
        # Get initial token
        self.credentials.refresh(Request())
    
    def _get_headers(self):
        """Get authorization headers for API requests."""
        # Refresh token if needed
        if not self.credentials.valid:
            self.credentials.refresh(Request())
        
        return {
            'Authorization': f'Bearer {self.credentials.token}',
            'Content-Type': 'application/json'
        }
    
    def _api_request(self, method: str, endpoint: str, data: Optional[Dict] = None):
        """Make an API request to Google Sheets."""
        base_url = "https://sheets.googleapis.com/v4/spreadsheets"
        url = f"{base_url}/{self.config.google_sheets_id}{endpoint}"
        
        headers = self._get_headers()
        
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, headers=headers, json=data)
        elif method.upper() == 'PUT':
            response = requests.put(url, headers=headers, json=data)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        if response.status_code not in [200, 201]:
            raise Exception(f"API request failed: {response.status_code} - {response.text}")
        
        return response.json()
    
    def _get_sheet_info(self):
        """Get information about the spreadsheet."""
        return self._api_request('GET', '')
    
    def _get_worksheet_id(self, sheet_name: str):
        """Get the worksheet ID for a given sheet name."""
        sheet_info = self._get_sheet_info()
        
        for sheet in sheet_info.get('sheets', []):
            if sheet['properties']['title'] == sheet_name:
                return sheet['properties']['sheetId']
        
        # Create new worksheet if not found
        return self._create_worksheet(sheet_name)
    
    def _create_worksheet(self, sheet_name: str):
        """Create a new worksheet."""
        data = {
            "requests": [{
                "addSheet": {
                    "properties": {
                        "title": sheet_name,
                        "gridProperties": {
                            "rowCount": 1000,
                            "columnCount": 50
                        }
                    }
                }
            }]
        }
        
        response = self._api_request('POST', ':batchUpdate', data)
        return response['replies'][0]['addSheet']['properties']['sheetId']
    
    def _get_values(self, range_name: str):
        """Get values from a specific range."""
        # URL encode the range name to handle spaces and special characters
        encoded_range = urllib.parse.quote(range_name, safe='')
        endpoint = f"/values/{encoded_range}"
        try:
            response = self._api_request('GET', endpoint)
            return response.get('values', [])
        except:
            return []
    
    def _update_values(self, range_name: str, values: List[List]):
        """Update values in a specific range."""
        # URL encode the range name to handle spaces and special characters
        encoded_range = urllib.parse.quote(range_name, safe='')
        endpoint = f"/values/{encoded_range}"
        data = {
            "values": values,
            "majorDimension": "ROWS"
        }

        return self._api_request('PUT', endpoint + "?valueInputOption=RAW", data)
    
    def _get_balance_date(self) -> str:
        """
        Get the appropriate date for balance tracking based on end-of-day logic.

        If current time is before the cutoff hour, use previous day's date.
        This is useful for end-of-day balance collection run at midnight.

        Returns:
            Formatted date string
        """
        now = datetime.now()

        # If current hour is before cutoff hour, use previous day
        if now.hour < self.config.end_of_day_cutoff_hour:
            balance_date = now - timedelta(days=1)
        else:
            balance_date = now

        return balance_date.strftime(self.config.date_format)

    def _get_all_account_emails(self, balances: List[AccountBalance]) -> List[str]:
        """Get all unique account emails from balances, sorted alphabetically."""
        emails = set()
        for balance in balances:
            emails.add(balance.email)
        return sorted(list(emails))
    
    def _setup_sheet_structure(self, sheet_name: str, all_emails: List[str]) -> None:
        """Setup the basic sheet structure with Account column and email addresses."""
        try:
            # Get current data from column A
            current_data = self._get_values(f"{sheet_name}!A:A")
            
            if not current_data or (current_data and current_data[0][0] != "Account"):
                # Setup the basic structure
                print("Setting up sheet structure...")
                
                # Prepare data with Account header and emails
                data_to_update = [["Account"]]
                for email in all_emails:
                    data_to_update.append([email])
                
                # Update column A
                self._update_values(f"{sheet_name}!A:A", data_to_update)
                print(f"Sheet structure setup complete with {len(all_emails)} accounts")
            else:
                # Check if we need to add any new emails
                existing_emails = [row[0] for row in current_data[1:] if row]  # Skip header
                new_emails = [email for email in all_emails if email not in existing_emails]
                
                if new_emails:
                    print(f"Adding {len(new_emails)} new accounts to sheet...")
                    start_row = len(current_data) + 1
                    
                    new_data = [[email] for email in new_emails]
                    range_name = f"{sheet_name}!A{start_row}:A{start_row + len(new_data) - 1}"
                    self._update_values(range_name, new_data)
                    
        except Exception as e:
            print(f"Error setting up sheet structure: {e}")
            raise
    
    def _find_next_available_column(self, sheet_name: str) -> str:
        """Find the next available column for adding today's balance data."""
        try:
            # Get the first row to check existing headers
            first_row = self._get_values(f"{sheet_name}!1:1")
            
            if not first_row:
                return 'B'  # Start with column B if no data
            
            # Find the first empty column after "Account"
            next_col_index = len(first_row[0]) + 1
            
            # Convert to column letter (A=1, B=2, etc.)
            column_letter = ''
            while next_col_index > 0:
                next_col_index -= 1
                column_letter = chr(65 + (next_col_index % 26)) + column_letter
                next_col_index //= 26
            
            return column_letter
            
        except Exception as e:
            print(f"Error finding next available column: {e}")
            return 'B'  # Default to column B
    
    def update_balances(self, balances: List[AccountBalance], summary_stats: Dict[str, Any]) -> bool:
        """
        Update Google Sheets with current balance data using daily column format.
        
        Args:
            balances: List of AccountBalance objects
            summary_stats: Summary statistics dictionary
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            sheet_name = self.config.sheet_name
            
            # Get all unique account emails
            all_emails = self._get_all_account_emails(balances)
            
            # Setup the basic sheet structure with account emails
            self._setup_sheet_structure(sheet_name, all_emails)
            
            # Find the next available column for today's data
            column_letter = self._find_next_available_column(sheet_name)
            
            # Get the appropriate date for the header (end-of-day logic)
            today = self._get_balance_date()
            
            # Check if today's column already exists
            first_row = self._get_values(f"{sheet_name}!1:1")
            if first_row and len(first_row[0]) > 1:
                # Check if today's date is already in the headers
                if today in first_row[0]:
                    # Find the column with today's date
                    today_col_index = first_row[0].index(today) + 1
                    column_letter = chr(64 + today_col_index)  # Convert to letter
                    print(f"Column for {today} already exists, updating existing data...")
                else:
                    # Add today's date as header
                    self._update_values(f"{sheet_name}!{column_letter}1", [[today]])
                    print(f"Added new column for {today} at column {column_letter}")
            else:
                # Add today's date as header
                self._update_values(f"{sheet_name}!{column_letter}1", [[today]])
                print(f"Added new column for {today} at column {column_letter}")
            
            # Create a mapping of email to total USDT balance
            balance_map = {}
            for balance in balances:
                balance_map[balance.email] = float(balance.total_usdt)
            
            # Get current account emails from the sheet
            current_emails_data = self._get_values(f"{sheet_name}!A:A")
            current_emails = [row[0] for row in current_emails_data[1:] if row]  # Skip header
            
            # Prepare balance data for the current column
            balance_data = []
            for email in current_emails:
                usdt_balance = balance_map.get(email, 0.0)
                balance_data.append([usdt_balance])
            
            # Update the balance column
            if balance_data:
                range_name = f"{sheet_name}!{column_letter}2:{column_letter}{len(balance_data) + 1}"
                self._update_values(range_name, balance_data)
            
            print(f"Successfully updated Google Sheets with {len(balance_data)} balance entries for {today}")
            print(f"Sheet ID: {self.config.google_sheets_id}")
            return True
            
        except Exception as e:
            print(f"Error updating Google Sheets: {e}")
            print(f"Error type: {type(e).__name__}")
            return False
