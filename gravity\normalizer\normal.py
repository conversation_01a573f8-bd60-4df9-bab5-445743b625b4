import csv
import dataclasses
import os
import pandas as pd
from typing import Optional, List, Tuple, Dict, Any

import warnings
warnings.filterwarnings("ignore", message="Workbook contains no default style", category=UserWarning)

@dataclasses.dataclass
class Transfer:
    asset: str
    amount: float
    ts: float
    exchange: str
    fee: float
    address: str
    tag: Optional[str] = None
    txid: Optional[str] = None
    receiver: Optional[str] = None
    value: Optional[float] = None
    potential_match: Optional[str] = None
    does_txid_match_db: Optional[bool] = None

    @classmethod
    def from_row(cls, row):
        try:
            fee = float(row["fee"] if ("fee" in row) or row["fee"] else 0)
        except ValueError:
            fee = 0
        return cls(
            asset=row["asset"],
            amount=float(row["amount"]),
            ts=float(row["ts"]),
            exchange=row["exchange"],
            fee=fee,
            address=row["address"],
            tag=row.get("tag"),
            txid=row.get("txid"),
            receiver=row.get("receiver"),
            value=row.get("value"),
            potential_match=row.get("potential_match"),
            does_txid_match_db=row.get("does_txid_match_db"),
        )
    def to_dict(self):
        return dataclasses.asdict(self)
    def keys(self):
        return Transfer.__dataclass_fields__.keys()

    def values(self):
        return Transfer.__dataclass_fields__.values()

    def get(self, keyname, value):
        return Transfer.__dataclass_fields__.get(keyname, value)

    def __hash__(self):
        return hash((self.asset, self.amount, self.ts, self.exchange))

class Withdrawals(Transfer):
    pass

class Deposits(Transfer):
    pass

class Normalizer:
    exchange = "Sample"
    skip_rows = 0
    encoding = "utf-8"

    columns: List[str] = None
    def __init__(self, _columns: List[str] = None):
        self.columns = _columns

    def normalize_row(self, row, file_name) -> Transfer:
        pass

    def normalize_dir(self, output_path: str, input_path: str):
        with open(
            os.path.join(output_path, f"{self.exchange}_deposits.csv"), "w", newline=''
        ) as csv_deposit_file:
            csv_deposit_writer = csv.DictWriter(
                csv_deposit_file, fieldnames=Deposits.__dataclass_fields__.keys()
            )
            csv_deposit_writer.writeheader()
            with open(
                os.path.join(output_path, f"{self.exchange}_withdrawals.csv"), "w", newline=''
            ) as csv_withdrawal_file:
                csv_withdrawal_writer = csv.DictWriter(
                    csv_withdrawal_file,
                    fieldnames=Withdrawals.__dataclass_fields__.keys(),
                )
                csv_withdrawal_writer.writeheader()
                for file in os.listdir(input_path):
                    print("Normalizing file", file)
                    self.normalize_file(
                        os.path.join(input_path, file),
                        csv_deposit_writer,
                        csv_withdrawal_writer,
                    )

    def _file_rows(self, file_name: str):
        df: pd.DataFrame = None
        read_func = self.xlsx_file_reader

        if file_name.endswith(".csv"):
            read_func = self.csv_file_reader
        elif file_name.endswith(".tsv"):
            read_func = self.tsv_file_reader

        df = read_func(file_name)
        if self.columns:
            df.columns = self.columns

        for _, row in df.iterrows():
            yield row

    def csv_file_reader(self, file_name: str):
        return pd.read_csv(file_name, encoding=self.encoding, skiprows=self.skip_rows, keep_default_na=False)

    def tsv_file_reader(self, file_name: str):
        return pd.read_csv(file_name, encoding=self.encoding, skiprows=self.skip_rows, sep="\t", keep_default_na=False)

    def xlsx_file_reader(self, file_name: str):
        df = pd.read_excel(file_name, skiprows=self.skip_rows, sheet_name=None, index_col=None, keep_default_na=False)
        return pd.concat(df.values())

    def normalize_file(
        self,
        file_name: str,
        csv_deposit_writer: csv.DictWriter,
        csv_withdrawal_writer: csv.DictWriter,
    ):
        print(file_name)
        if os.path.isdir(file_name):
            for l in os.listdir(file_name):
                self.normalize_file(os.path.join(file_name, l), csv_deposit_writer, csv_withdrawal_writer)
            return
        # TODO: add capability to convert more file extensions
        if file_name.endswith("_deposits.csv") or file_name.endswith("_withdrawals.csv") or os.path.isdir(file_name):
            print(f"Conflicting name {file_name}, please avoid _deposits.csv and _withdrawals.csv file endings")
            return

        for row in self._file_rows(file_name):
            transfer = self.normalize_row(row, file_name)
            if isinstance(transfer, Deposits):
                csv_deposit_writer.writerow(dataclasses.asdict(transfer))
            elif isinstance(transfer, Withdrawals):
                csv_withdrawal_writer.writerow(dataclasses.asdict(transfer))
            elif transfer is not None:
                print(f"Unknown transfer type {type(transfer)}")

    def extract_addr_tags(self, raw_addr:str) -> Tuple[str, Optional[str]]:
        if raw_addr is None:
            return None, None

        result = raw_addr.split(":")
        if "dt" in raw_addr:
            result = raw_addr.split("?dt=")
        elif "memo" in raw_addr:
            result = raw_addr.split("?memoId=")

        if len(result) > 1:
            return result[0], result[1]

        return result[0], None