from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals
import pandas as pd

class BitstampNormalizer(Normalizer):
    exchange = "bitstamp"

    def normalize_row(self, row,  file_name: str):
        # 1/1/2023 10:31:21 AM

        if str(row["datetime"]) == "NaT":
            return None

        time_format = "%Y-%m-%d %H:%M:%S"
        try:
            ts = row["datetime"].timestamp()
        except:
            print(f'Skipping row due to bad ts format: {str(row["datetime"])}')
            return None
    

        coin = row["currency"].upper()
        operation_type = row["type"]
        amount = float(row["amount_currency"])
        
        addr, tag = self.extract_addr_tags(row["destination_address"])

        if "Deposit" in operation_type:
            return Deposits(
                asset=coin,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=row['on_chain_trxid'],
            )
        elif "Withdrawal" in operation_type:
            return Withdrawals(
                asset=coin,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=addr,
                tag=tag,
                txid=row['on_chain_trxid'],
            )