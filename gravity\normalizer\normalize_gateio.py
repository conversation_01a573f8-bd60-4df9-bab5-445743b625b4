import csv
import dataclasses
import os.path
from datetime import datetime
import pandas as pd
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class GateioNormalizer(Normalizer):
    exchange = "gateio"
    def normalize_row(self, row, file_name):
        time_format = "%Y-%m-%d %H:%M:%S"

        if "deposit" in file_name:
            return Deposits(
                row['Coin'],
                float(row['Amount']),
                int(datetime.strptime(row["Time"], time_format).timestamp()) + (60 * 60),
                self.exchange,
                None,
                row["Address"].split(" ")[0],
                None,
                row['TxID'],
            )
        elif "withdraw" in file_name:
            return Withdrawals(
                row['Coin'],
                float(row['Amount']),
                int(datetime.strptime(row["Time"], time_format).timestamp()) - (60 * 60),
                self.exchange,
                float(row["Amount"]) - float(row["Amount Received"]),
                row["Address"].split(" ")[0],
                row["Address"].split(" ")[1] if len(row["Address"].split(" ")) > 1 else None,
                row['TxID'],
            )
