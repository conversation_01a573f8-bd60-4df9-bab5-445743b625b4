import csv
import os
import argparse
import collections

if __name__ == "__main__":
    parser = argparse.ArgumentParser("Generate address book from list of transfers")

    # parser.add_argument("exchange", help="Exchange name")

    parser.add_argument("-i",  "--input-path", help="Path to directory with all transfers")
    parser.add_argument("-o", "--output", help="Path to output file (including file name)")

    args = parser.parse_args()

    directory = args.input_path
    known_addresses = collections.defaultdict(dict)
    with open(args.output, "w", newline='') as csv_addresses:
        wat = csv.DictWriter(
            csv_addresses, fieldnames=["exchange", "asset", "address", "tag", "network"]
        )
        wat.writeheader()
        for file_name in os.listdir(directory):
            with open(os.path.join(directory, file_name)) as csv_deposit_file:
                print(f"Reading {csv_deposit_file.name}")
                csv_deposit_reader = csv.DictReader(csv_deposit_file)
                for row in csv_deposit_reader:
                    target_exchange = row["exchange_to"]
                    coin = row["gt_asset"]

                    address_line = row["address"]
                    tag = row["memo"]
                    network = row["network"]

                    if coin not in known_addresses[target_exchange]:
                        known_addresses[target_exchange][coin] = []
                    
                    address = {
                        "address": address_line,
                        "tag": tag,
                        "network": network,
                    }

                    if address not in known_addresses[target_exchange][coin]:
                        known_addresses[target_exchange][coin].append(address)
                        wat.writerow(
                            {
                                "exchange": target_exchange,
                                "asset": coin,
                                "address": address_line,
                                "tag": tag,
                                "network": network,
                            }
                        )
