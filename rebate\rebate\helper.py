import asyncio
from asyncio import CancelledError
from typing import Callable, TypeVar, Union, Coroutine, Optional, Dict
from .config import log
_ReqType = TypeVar("T")

def await_looper_interval(sleep_: Union[float, int]):
    def decorator(fn: Callable[[_ReqType], Coroutine]):
        async def decorated(*args: _ReqType):
            while asyncio.get_event_loop().is_running():
                try:
                    while asyncio.get_event_loop().is_running():
                        try:
                            await asyncio.sleep(sleep_)
                            await fn(*args)
                        except CancelledError:
                            log.debug(f"Task {asyncio.current_task().get_name()} is canceled")
                            return
                        except:
                            log.traceback()
                except:
                    pass

        decorated.__qualname__ = f"{fn.__qualname__}_looper"
        return decorated
    return decorator
_ongoing_tasks: Dict[int, asyncio.Task] = {}

async def _logged_task(func: Coroutine):
    try:
        return await func
    except CancelledError:
        raise
    except BaseException:
        log.error("Task failed with exception")
        log.traceback()
        raise


def _cleanup_task(task: asyncio.Task):
    del _ongoing_tasks[id(task)]

def create_task(func: Coroutine, loop: Optional[asyncio.AbstractEventLoop] = None) -> asyncio.Task:
    if loop is None:
        loop = asyncio.get_event_loop()

    task = loop.create_task(_logged_task(func))
    _ongoing_tasks[id(task)] = task
    task.add_done_callback(_cleanup_task)
    return task
