import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
from typing import Dict, Any, List
from datetime import datetime
import os


class PosconExcelGenerator:
    """Generate POSCON Excel reports with before/after adjustments and exchange details."""
    
    def __init__(self, output_path: str):
        self.output_path = output_path
        self.workbook = None
    
    def generate_poscon_report(self, analysis_results: Dict[str, Any]) -> str:

        print(f"Generating Excel report: {self.output_path}")

        self.workbook = Workbook()

        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])

        exchange_analysis = analysis_results.get('exchange_details', {})
        explanations = analysis_results.get('explanations', {})
        threshold = analysis_results.get('threshold', 10000)

        is_multi_day = 'analysis_windows' in analysis_results

        if is_multi_day:
            self._add_multi_day_summary(analysis_results)

        self._add_summary_before_adjustments(exchange_analysis, threshold)
        self._add_summary_after_adjustments(exchange_analysis, explanations, threshold)

        self._add_exchange_detail_sheets(exchange_analysis, explanations)

        self._ensure_output_directory()
        self.workbook.save(self.output_path)

        print(f"Excel report saved: {self.output_path}")
        return self.output_path

    def _add_multi_day_summary(self, analysis_results: Dict[str, Any]):
        ws = self.workbook.create_sheet("Multi-Day Analysis Summary")

        ws['A1'] = "POSCON Multi-Day Analysis Summary"
        ws['A1'].font = Font(size=16, bold=True)

        analysis_windows = analysis_results.get('analysis_windows', [])
        threshold = analysis_results.get('threshold', 10000)

        ws['A3'] = f"Total Analysis Windows: {len(analysis_windows)}"
        ws['A4'] = f"Threshold: ${threshold:,.0f}"
        ws['A5'] = f"Total Problematic Exchanges: {len(analysis_results.get('problematic_exchanges', []))}"

        # Headers for window details
        headers = ['Target Date', 'Window Dates', 'Problematic Exchanges', 'Flagged Assets']
        for col, header in enumerate(headers, start=1):
            cell = ws.cell(row=7, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # Add window details
        for row, window in enumerate(analysis_windows, start=8):
            target_date = window['target_date']
            window_dates = ', '.join([str(d) for d in window['window_dates']])
            window_results = window['results']

            problematic_exchanges = window_results.get('problematic_exchanges', [])
            flagged_assets_count = sum(
                len(assets) for assets in window_results.get('flagged_assets', {}).values()
            )

            ws.cell(row=row, column=1, value=str(target_date))
            ws.cell(row=row, column=2, value=window_dates)
            ws.cell(row=row, column=3, value=', '.join(problematic_exchanges) if problematic_exchanges else 'None')
            ws.cell(row=row, column=4, value=flagged_assets_count)

        self._format_worksheet(ws)

    def _add_summary_before_adjustments(self, exchange_analysis: Dict, threshold: float):
        ws = self.workbook.create_sheet("Summary - Before Adjustments")

        exchange_date_data = {} 
        all_exchanges = set()
        all_dates = set()

        for exchange_date_key, analysis_df in exchange_analysis.items():
            if analysis_df.empty:
                continue

            # Parse exchange and date from the key
            if '_' in exchange_date_key:
                # Multi-day format: "exchange_date"
                parts = exchange_date_key.rsplit('_', 1)
                exchange = parts[0]
                date_str = parts[1]
            else:
                # Single-day format: just exchange name
                exchange = exchange_date_key
                # Get the date from the dataframe columns
                date_columns = [col for col in analysis_df.columns
                              if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
                date_str = str(date_columns[0]) if date_columns else 'unknown'

            all_exchanges.add(exchange)
            all_dates.add(date_str)

            date_columns = [col for col in analysis_df.columns
                          if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]

            if date_columns:
                date_col = date_columns[0]
                total_unexplained = analysis_df[date_col].sum()

                if exchange not in exchange_date_data:
                    exchange_date_data[exchange] = {}
                exchange_date_data[exchange][date_str] = total_unexplained

        all_exchanges = sorted(list(all_exchanges))
        all_dates = sorted(list(all_dates))

        pivot_data = []
        for exchange in all_exchanges:
            row_data = {'Exchange': exchange}

            for date_str in all_dates:
                if exchange in exchange_date_data and date_str in exchange_date_data[exchange]:
                    row_data[date_str] = exchange_date_data[exchange][date_str]
                else:
                    row_data[date_str] = 0.0

            pivot_data.append(row_data)
        
        if pivot_data:
            summary_df = pd.DataFrame(pivot_data)
            columns = ['Exchange'] + [str(date) for date in all_dates]
            summary_df = summary_df[columns]
        else:
            summary_df = pd.DataFrame(columns=['Exchange'])
        
        ws['A1'] = "POSCON Analysis - Summary Before Adjustments"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A2'] = f"Threshold: ${threshold:,.0f}"
        ws['A3'] = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        if not summary_df.empty:
            for r_idx, row in enumerate(dataframe_to_rows(summary_df, index=False, header=True), start=5):
                for c_idx, value in enumerate(row, start=1):
                    cell = ws.cell(row=r_idx, column=c_idx, value=value)
                    if r_idx == 5:  # Header row
                        cell.font = Font(bold=True)
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    elif isinstance(value, (int, float)) and c_idx > 1:  # Format numeric columns (excluding Exchange column)
                        cell.number_format = '#,##0.00'
                        # Highlight cells over threshold
                        if abs(value) > threshold:
                            cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
        
        self._format_worksheet(ws)
    
    def _add_summary_after_adjustments(self, exchange_analysis: Dict, explanations: Dict, threshold: float):
        ws = self.workbook.create_sheet("Summary - After Adjustments")

        exchange_date_data = {} 
        all_exchanges = set()
        all_dates = set()

        for exchange_date_key, analysis_df in exchange_analysis.items():
            if analysis_df.empty:
                continue

            # Parse exchange and date from the key
            if '_' in exchange_date_key:
                # Multi-day format: "exchange_date"
                parts = exchange_date_key.rsplit('_', 1)
                exchange = parts[0]
                date_str = parts[1]
            else:
                # Single-day format: just exchange name
                exchange = exchange_date_key
                # Get the date from the dataframe columns
                date_columns = [col for col in analysis_df.columns
                              if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
                date_str = str(date_columns[0]) if date_columns else 'unknown'

            all_exchanges.add(exchange)
            all_dates.add(date_str)

            date_columns = [col for col in analysis_df.columns
                          if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]

            if date_columns:
                date_col = date_columns[0]
                original_unexplained = analysis_df[date_col].sum()

                total_adjustment = 0.0
                if exchange in explanations:
                    for asset_key, explanation in explanations[exchange].items():
                        explanation_date = None
                        if '_' in asset_key:
                            asset_parts = asset_key.rsplit('_', 1)
                            if len(asset_parts) > 1:
                                try:
                                    explanation_date = asset_parts[1]
                                except:
                                    pass

                        if explanation_date is None or explanation_date == date_str:
                            total_adjustment += explanation.get('total_adjustment_usd', 0.0)

                adjusted_unexplained = original_unexplained - total_adjustment

                if exchange not in exchange_date_data:
                    exchange_date_data[exchange] = {}
                exchange_date_data[exchange][date_str] = {
                    'original': original_unexplained,
                    'adjusted': adjusted_unexplained
                }

        all_exchanges = sorted(list(all_exchanges))
        all_dates = sorted(list(all_dates))

        pivot_data = []
        for exchange in all_exchanges:
            row_data = {'Exchange': exchange}

            for date_str in all_dates:
                if exchange in exchange_date_data and date_str in exchange_date_data[exchange]:
                    row_data[date_str] = exchange_date_data[exchange][date_str]['adjusted']
                else:
                    row_data[date_str] = 0.0

            pivot_data.append(row_data)
        
        if pivot_data:
            summary_df = pd.DataFrame(pivot_data)
            columns = ['Exchange'] + [str(date) for date in all_dates]
            summary_df = summary_df[columns]
        else:
            summary_df = pd.DataFrame(columns=['Exchange'])
        
        ws['A1'] = "POSCON Analysis - Summary After Adjustments"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A2'] = f"Threshold: ${threshold:,.0f}"
        ws['A3'] = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        if not summary_df.empty:
            for r_idx, row in enumerate(dataframe_to_rows(summary_df, index=False, header=True), start=5):
                for c_idx, value in enumerate(row, start=1):
                    cell = ws.cell(row=r_idx, column=c_idx, value=value)
                    if r_idx == 5:  # Header row
                        cell.font = Font(bold=True)
                        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    elif isinstance(value, (int, float)) and c_idx > 1:  # Format numeric columns (excluding Exchange column)
                        cell.number_format = '#,##0.00'
                        if abs(value) > threshold:
                            cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
                        elif abs(value) <= threshold and value != 0:
                            cell.fill = PatternFill(start_color="CCFFCC", end_color="CCFFCC", fill_type="solid")
        
        self._format_worksheet(ws)
    
    def _add_exchange_detail_sheets(self, exchange_analysis: Dict, explanations: Dict):
        for exchange_date_key, analysis_df in exchange_analysis.items():
            if analysis_df.empty:
                continue

            # Parse exchange and date from the key
            if '_' in exchange_date_key:
                # Multi-day format: "exchange_date"
                parts = exchange_date_key.rsplit('_', 1)  # Split from right to handle exchanges with underscores
                exchange = parts[0]
                date_str = parts[1]
            else:
                # Single-day format: just exchange name
                exchange = exchange_date_key
                # Get the date from the dataframe columns
                date_columns = [col for col in analysis_df.columns
                              if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]
                date_str = str(date_columns[0]) if date_columns else 'unknown'

            sheet_name = f"{exchange}-{date_str}"
            if len(sheet_name) > 31:
                sheet_name = f"{exchange[:20]}-{date_str}"

            ws = self.workbook.create_sheet(sheet_name)

            ws['A1'] = f"Exchange: {exchange}"
            ws['A1'].font = Font(size=14, bold=True)
            ws['A2'] = f"Date: {date_str}"
            ws['A2'].font = Font(size=12, bold=True)

            ws['A4'] = "Asset-Level Analysis"
            ws['A4'].font = Font(size=12, bold=True)

            date_columns = [col for col in analysis_df.columns
                          if col not in ['Exchange', 'Asset', 'Total_Unexplained', 'Flagged']]

            if date_columns:
                date_col = date_columns[0]

                asset_data = analysis_df[['Asset', date_col]].copy()
                asset_data = asset_data.rename(columns={date_col: 'Unexplained_USD'})
                asset_data = asset_data.sort_values('Unexplained_USD', key=abs, ascending=False)

                for r_idx, row in enumerate(dataframe_to_rows(asset_data, index=False, header=True), start=6):
                    for c_idx, value in enumerate(row, start=1):
                        cell = ws.cell(row=r_idx, column=c_idx, value=value)
                        if r_idx == 6:  # Header row
                            cell.font = Font(bold=True)
                            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                        elif isinstance(value, (int, float)) and c_idx == 2:  # Format USD column
                            cell.number_format = '#,##0.00'

                if exchange in explanations:
                    current_row = len(asset_data) + 8
                    ws[f'A{current_row}'] = "Systematic Explanations"
                    ws[f'A{current_row}'].font = Font(size=12, bold=True)
                    current_row += 2

                    for asset_key, explanation in explanations[exchange].items():
                        explanation_date = None
                        if '_' in asset_key:    
                            asset_parts = asset_key.rsplit('_', 1)
                            if len(asset_parts) > 1:
                                try:
                                    explanation_date = asset_parts[1]
                                except:
                                    pass

                        if explanation_date is None or explanation_date == date_str:
                            asset_name = asset_key.split('_')[0] if '_' in asset_key else asset_key

                            ws[f'A{current_row}'] = f"Asset: {asset_name}"
                            ws[f'A{current_row}'].font = Font(bold=True)
                            current_row += 1

                            adjustments = explanation.get('adjustments', [])
                            total_adj = explanation.get('total_adjustment_usd', 0)

                            ws[f'B{current_row}'] = f"Total Adjustment: ${total_adj:,.2f}"
                            current_row += 1

                            for adj in adjustments:
                                ws[f'C{current_row}'] = f"• {adj['case']}: {adj['count']} transfers, ${adj['adjustment_usd']:,.2f}"
                                current_row += 1
                                ws[f'D{current_row}'] = f"  {adj['reason']}"
                                current_row += 1
                        
                        current_row += 1
                
                self._format_worksheet(ws)
    
    def _format_worksheet(self, worksheet):

        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value is not None:
                    cell.border = thin_border
    
    def _ensure_output_directory(self):
        output_dir = os.path.dirname(self.output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir) 