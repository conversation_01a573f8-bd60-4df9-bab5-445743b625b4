from datetime import datetime, timezone
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class BybitNormalizer(Normalizer):
    exchange = "bybit"
    skip_rows = 1

    def normalize_row(self, row, file_name):

        # 2024-12-31 06:02:52
        time_format = "%Y-%m-%d %H:%M:%S"

        timestamp = float(datetime.strptime(row["Date"], time_format).timestamp())
        asset = row['Asset']
        txid = row['Tx ID']
        amount = abs(float(row['Amount']))

        address = row["Received Address"]
        tag = None

        if row["Type"] == "Deposit":
            return Deposits(
                asset=asset,
                amount=amount,
                ts=timestamp,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid,
            )
        elif row["Type"] == "Withdraw":
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=timestamp,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid,
            )
        
        return None