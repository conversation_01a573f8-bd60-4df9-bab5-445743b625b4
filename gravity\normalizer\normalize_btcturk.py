import math

from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class BtcturkNormalizer(Normalizer):
    exchange = "btcturk_hft"

    def normalize_row(self, row, file_name):
        ts = float(math.ceil(row["requested_ts"]))
        asset = row["asset"]
        txid = row["txid"]
        amount = float(row["amount"])
        address = row["address"]
        tag = row["memo"]

        if "deposits" in file_name:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid,
            )
        elif "withdraw" in file_name:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid,
            )