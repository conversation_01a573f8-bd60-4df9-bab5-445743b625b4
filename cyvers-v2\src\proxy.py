import logging
import time
import asyncio
import os
import pickle

from pprint import pformat
from multidict import CIMultiDictProxy
from typing import Dict, Any, List
from aiohttp import ClientSession, ClientError

from . import ctx
from .dto import CyversAlert
from .misc import ERROR_COUNTER


class CyversProxy:
    def __init__(self, req_log: logging.Logger):
        self.log = logging.getLogger(self.__class__.__name__)
        self.__cfg: Dict[str, Any] = ctx.ctx.cfg.yml.get("proxy")
        self.__am_ep: str = ctx.ctx.cfg.yml.get("alertmanager").get("url")
        self.loop = asyncio.get_event_loop()
        self.req_log: logging.Logger = req_log

        self.__pick_file = os.path.abspath(os.path.join(ctx.ctx.cfg.args.data_dir, "alerts.data"))

        if not os.path.exists(os.path.dirname(self.__pick_file)):
            os.mkdir(os.path.dirname(self.__pick_file))

    def refill(self) -> None:
        if os.path.exists(self.__pick_file):
            try:
                with open(self.__pick_file, "rb") as f:
                    self.log.info(f"Loading alert history from {self.__pick_file}")
                    ctx.ctx.alerts = pickle.loads(f.read())
                    self.log.info(f"Loaded {len(ctx.ctx.alerts)} from {self.__pick_file}")
            except pickle.PickleError as e:
                self.log.warning(
                    f"Unable to load alert history from {self.__pick_file}\n{e}",
                )

    def flush(self) -> None:
        """
        Just to be safe storing alert state in file
        """
        with open(self.__pick_file, "wb") as f:
            self.log.info(f"Dumping alerts {len(ctx.ctx.alerts)} into {self.__pick_file}")
            pickle.dump(ctx.ctx.alerts, f)

    async def webhook_handler(self, payload: Dict[str, Any], headers: CIMultiDictProxy[str]) -> None:
        try:
            alert = CyversAlert(payload, headers)
        except Exception as e:
            self.log.error(f"Failed to parse Cyvers webhook", exc_info=e)
            ERROR_COUNTER.labels(scope=self.__class__.__name__.lower()).inc()
            raise e

        ctx.ctx.alerts.append(alert)
        self.log.info(f'Handling cyvers alert \"{alert.json}"\"')
        ctx.ctx.task_dump.append(self.loop.create_task(self.webhook_forward(alert), name=f"cyvers-webhook-{alert.id}"))

    async def webhook_forward(self, alert: CyversAlert) -> None:
        self.log.info(f"Forwarding alert to AlartManager id={alert.id}")
        payload: List[Dict[str:Any]] = alert.to_prometheus_up(self.__cfg.get("realm"))
        self.req_log.info(f'Alertmanager call payload="{payload}"')
        try:
            async with ClientSession() as client:
                rsp = await client.request(
                    method="POST",
                    url=f"{self.__am_ep}/api/v2/alerts",
                    json=payload,
                )
                if rsp.status == 200:
                    alert.last_sent = time.time()
                    self.log.info(f"AlertManager response {rsp.status}")
                else:
                    ERROR_COUNTER.labels(scope=self.__class__.__name__.lower()).inc()
                    rsp_text = await rsp.text()
                    self.log.warning(f'AlertManager response {rsp.status}, content "{rsp_text}"')
        except ClientError as e:
            self.log.warning(f"AlertManager call failed response {rsp.status}", exc_info=e)

    async def fan(self) -> None:
        ctx.ctx.tid_bump()
        if len(ctx.ctx.alerts) == 0:
            return

        alert_duration: float = float(self.__cfg.get("alert_duration_seconds"))
        for alert in ctx.ctx.alerts:
            if alert.startsAt <= time.time() - alert_duration:
                ctx.ctx.alerts.remove(alert)
                self.log.info(f"Alert expired id={alert.id} ttl={round(alert.startsAt + alert_duration - time.time())}")
            else:
                self.log.info(
                    f"Alert retained id={alert.id} ttl={round(alert.startsAt + alert_duration - time.time())}"
                )
        self.flush()

        if len(ctx.ctx.alerts) == 0:
            return

        self.log.info("Resending known alerts to AlartManager")
        for alert in ctx.ctx.alerts:
            if alert.last_sent < time.time() - self.__cfg.get("resend_seconds"):
                ctx.ctx.task_dump.append(
                    self.loop.create_task(self.webhook_forward(alert), name=f"cyvers-resend-{alert.id}")
                )
            else:
                self.log.info(
                    f"Alert resend id={alert.id} after={ round(alert.last_sent + self.__cfg.get("resend_seconds") - time.time()) }s"
                )
