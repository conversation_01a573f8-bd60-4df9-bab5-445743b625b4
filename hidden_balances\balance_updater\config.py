"""
Configuration management for the untracked balances module.
"""

import os
import yaml
from typing import List, Optional
from dataclasses import dataclass


@dataclass
class Config:
    """Configuration class for Binance API and application settings."""
    api_key: str
    api_secret: str
    emails: List[str]
    historical_date: Optional[str] = None

    # Google Sheets configuration
    google_sheets_id: Optional[str] = None
    google_credentials_path: Optional[str] = None
    sheet_name: str = ""

    # Balance tracking configuration
    use_daily_columns: bool = True  # Use daily column format for balance tracking
    date_format: str = "%Y-%m-%d"  # Format for date headers
    end_of_day_cutoff_hour: int = 0  # Hour (0-23) after which to use previous day's date

    # API settings
    base_url: str = "https://api.binance.com"
    timeout: int = 15
    page_size: int = 20


def load_config(config_path: str = "config.yml") -> Config:
    """
    Load configuration from YAML file.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Config object with loaded settings
        
    Raises:
        FileNotFoundError: If config file doesn't exist
        ValueError: If required fields are missing
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, "r") as f:
        config_data = yaml.safe_load(f)
    
    # Validate required fields
    required_fields = ["api_key", "api_secret"]
    for field in required_fields:
        if field not in config_data:
            raise ValueError(f"Required configuration field missing: {field}")
    
    return Config(
        api_key=config_data["api_key"],
        api_secret=config_data["api_secret"],
        emails=config_data.get("emails", []),
        historical_date=config_data.get("historical_date"),
        google_sheets_id=config_data.get("google_sheets_id"),
        google_credentials_path=config_data.get("google_credentials_path", "credentials.json"),
        sheet_name=config_data.get("sheet_name", "Balances"),
        use_daily_columns=config_data.get("use_daily_columns", True),
        date_format=config_data.get("date_format", "%Y-%m-%d"),
        end_of_day_cutoff_hour=config_data.get("end_of_day_cutoff_hour", 0),
    )


def get_config() -> Config:
    """Get configuration with environment variable overrides."""
    config = load_config()
    
    return config
