# Normalizer - a simple exchange export file normalizer

Takes exchange export files and normalizes them to a common format.
Exchange export files need to be placed in a directory with matching name to the exchange nmme.
When pointing the normalizer to the directory, it will process all files in the directory matching a supported exchange name.
Supported file formats are : csv, xlsx, tsv

## Usage
```bash
python3 -m gravity.normalizer -i <input_path> -o <output_path> 
```


# Transfer Matcher: matching deposits and withdrawals

Takes normalized deposits and withdrawals and matches them to each other.
In addition to normalized deposits and withdrawals, it takes a cav file of all know exchange addresses that have been used in the past.
And a csv file fo all withdrawals made by capman_v1 in the given time period for witch the matching is done. (simply export fom capman withdrawals table)

## Usage
```bash
python3 -m gravity.transfer_matcher -i <input_path> -o <output_path> -c <capman_withdrawals_csv> -a <all_exchange_addresses_csv>
```
It had two modes of operation:
1. match all deposits and withdrawals by assets and all matched transfers in groups by assets
2. Report matches in as single file and unmatched deposits and withdrawals in separate files #TODO


#TODO:
1. Finish requerments.txt
2. BUILD up CI CD
3. Add tests simple and easy at minimum test imports 