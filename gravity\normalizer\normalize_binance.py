from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

def  asset_mapping(asset):
    mapping = {"LUNC": "LUNA"}
    if asset in mapping:
        return mapping[asset]
    return asset
class BinanceNormalizer(Normalizer):
    exchange = "binance"

    def normalize_row(self, row, file_name: str):
        time_format = "%y-%m-%d %H:%M:%S"
        if row["Status"] != "Completed":
            return
        if "deposit" in file_name.lower():
            try:
                txid = row["TXID"]
            except:
                txid = None
            return Deposits(
                asset_mapping(row['Coin']),
                float(row['Amount']),
                int(datetime.strptime(row["Date(UTC+0)"], time_format).timestamp()),
                self.exchange,
                None,
                row["Address"],
                None,
                txid
            )
        elif "withdraw" in file_name.lower():
            return Withdrawals(
                asset_mapping(row['Coin']),
                float(row['Amount']),
                int(datetime.strptime(row["Date(UTC+0)"], time_format).timestamp()),
                self.exchange,
                float(row["Fee"]),
                row["Address"],
                None,
                row['TXID'],
            )
