## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <your‑repo‑url>
   cd route‑analysis
   ```
2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## 🐘 Database Configuration

Copy the example file and fill in your Postgres credentials:

```yml
database:
  user: my_user
  password: my_secret_password
  database: capman
  host: localhost
  port: 5432
```

## 🚦 Usage

### Quick start (defaults)

```bash
python -m route_analysis
```

### Full CLI

```bash
python -m route_analysis \
  --config config.local.yml \
  --out route_analysis.xlsx \
  --days 30 \
  --min-savings-single 500 \
  --min-savings-multi 100
```

| Flag                   | Description                                           | Default               |
| ---------------------- | ----------------------------------------------------- | --------------------- |
| `--config`             | Path to YAML config                                   | `config.local.yml`    |
| `--out`                | Excel file name                                       | `route_analysis.xlsx` |
| `--days`               | Look‑back window in days                              | `30`                  |
| `--min-savings-single` | Minimum saving (USD) for single‑network opportunities | `500`                 |
| `--min-savings-multi`  | Minimum saving (USD) for multi‑network opportunities  | `100`                 |

---

## 💾 Output

An Excel workbook with two sheets:

| Sheet              | Description                                                       |
| ------------------ | ----------------------------------------------------------------- |
| **Single Network** | Routes where only one eligible network connects the two exchanges |
| **Multi Network**  | Routes with multiple eligible networks – highlights the cheapest  |

Each sheet is sorted by potential savings (descending) and includes asset, network, exchanges, fee metrics, and total potential savings in USD.

---

## 🗂️ Project Structure

```
.
├── __main__.py        # CLI entry point (python -m route_analysis)
├── analyses.py        # Polars transformation pipeline
├── db.py              # DB helpers & SQL queries
├── requirements.txt   # Python dependencies
├── config.example.yml # Configuration template (copy to config.local.yml)
└── README.md          
```


