import datetime, sys, time, traceback, logging

from typing import Optional, Dict

from selenium.webdriver import Firefox
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from prometheus_client import Gauge, Counter

log = logging.getLogger("bybit_tr_clicker")
log.addHandler(logging.StreamHandler(sys.stdout))

from .common import (
    TransactionInfoDao,
    Transaction,
    COMPANY_NAME,
    COMPANY_NUMBER,
    TAX_NUMBER,
    main_app,
    EXCHANGE_MAPPING,
)

DEPOSIT_REASON = "Internal transfer"

HOST = "https://www.bybit-tr.com"
DEPOSIT_HISTORY = f"{HOST}/user/assets/records/fiat/depositSpot"
WITHDRAW_HISTORY = f"{HOST}/user/assets/records/fiat/withdrawSpot"

SOURCE = 502

SOURCE_EXCHANE_NAME = {
    "btcturk_turkey": "BTCTurk.com",
    "okx_turkey": "OKX TR",
    "binance_turkey": "Binance TR",
}


class BybitTrClicker:

    def __init__(self, driver: Firefox, trx_dao: TransactionInfoDao, config: Dict):
        self.driver: Firefox = driver
        self.trx_dao: TransactionInfoDao = trx_dao
        self.bybit_tab: Optional[str] = None

        namespace = "gt_clicker"
        self._mon_loop_end = Gauge("loop_end", documentation="", namespace=namespace)
        self._mon_loop_end.set_to_current_time()
        self._mon_deposit_details_req = Gauge("deposit_details_req", documentation="", namespace=namespace)
        self._mon_deposit_decl_req = Gauge("deposit_decl_req", documentation="", namespace=namespace)
        self._mon_deposit_details_provided = Counter("deposit_details_provided", documentation="", namespace=namespace)
        self._mon_deposit_decl_provided = Counter("deposit_decl_provided", documentation="", namespace=namespace)

        self._mon_withdrawal_decl_req = Gauge("withdrawal_decl_req", documentation="", namespace=namespace)
        self._mon_withdrawal_decl_provided = Counter("withdrawal_decl_provided", documentation="", namespace=namespace)

        self._mon_is_logged_in = Gauge("logged_in", documentation="", namespace=namespace)
        self._mon_errors = Counter("errors", documentation="", namespace=namespace)

    def sleep(self, sleep_for, comment: Optional[str] = None):
        log.debug(f"sleeping for {sleep_for} ({comment})")
        time.sleep(sleep_for)

    def switch_to_bybit_tab(self):
        for wh in self.driver.window_handles:
            self.driver.switch_to.window(wh)
            url = self.driver.current_url
            log.info(f"Tab {wh} current url = {url}")
            if "www.bybit-tr.com" in url:
                self.bybit_tab = wh
                return
        raise Exception("Failed to find bybit tab")

    def check_language(self):
        if self.driver.find_element(By.XPATH, "//html").get_attribute("lang") != "en-TR":
            self.driver.find_element(By.XPATH, "//div[contains(@class, 'lang-selector__btn')]").click()
            self.sleep(0.5, "lang selector open")
            self.driver.find_element(
                By.XPATH, "//div[contains(@class, 'by-lang-selector__item') and normalize-space() = 'English(Turkey)']"
            ).click()
            self.sleep(5, "lang selected")

    def check_login_status(self):
        if self.driver.current_url != DEPOSIT_HISTORY:
            self.driver.get(DEPOSIT_HISTORY)
        self.driver.refresh()
        self.sleep(3, "waiting for login refresh to finish")

        self.check_language()
        log.info(f"Current title {self.driver.title}")
        if "Log In" in self.driver.title:
            self._mon_is_logged_in.set(0)
            raise Exception("Bybit not logged in")
        else:
            self._mon_is_logged_in.set(1)

    def get_transaction_details(self, row_elem) -> Transaction:
        log.debug("Opening transaction details")
        row_elem.find_element(
            By.XPATH, ".//td/span[contains(@class, 'funding-record__scan-btn-detail') and normalize-space()='Details']"
        ).click()
        self.sleep(1, "wait for transaction details to open")

        modal_window = self.driver.find_element(By.XPATH, "//div[contains(@class, 'cashinout-record__detail-modal')]")
        try:
            detail_elems = modal_window.find_elements(
                By.XPATH,
                ".//div[contains(@class, 'cashinout-record__detail-content')]//div[contains(@class, 'cashinout-record__detail-item')]",
            )
            details = {}
            for det in detail_elems:
                name = det.find_element(By.XPATH, ".//span[1]").text
                value = det.find_element(By.XPATH, ".//span[2]").text
                details[name.strip()] = value.strip()
                log.debug(f"Found transaction detail: '{name}' = '{value}'")
        finally:
            modal_window.send_keys(Keys.ESCAPE)
            self.sleep(1, "modal details window close animation")

        fee = float(details["Transaction Fee"].replace(",", "")) if "Transaction Fee" in details else 0.0
        return Transaction(
            asset=details["Coin"],
            amount=float(details["Qty"].replace(",", "")) + fee,
            txid=details.get("Txid"),
            address=details.get("Address"),
            fee=fee,
            ts=datetime.datetime.strptime(details["Time"], "%Y-%m-%d %H:%M:%S"),
            source=SOURCE,
            trx_id=None,
        )

    def populate_deposit_details(self, row_id: str, source_exchange):
        populate_details_button = self.driver.find_element(
            By.XPATH, f"//tbody/tr[@data-row-key = '{row_id}']/following-sibling::tr//button"
        )
        populate_details_button.click()
        self.sleep(1, "Travel rule deposit form open")
        try:
            modal_window = self.driver.find_element(By.XPATH, "//div[contains(@class, 'travel-rule__deposit-form')]")
            company_button = modal_window.find_element(
                By.XPATH,
                ".//div[@id='travelRuleoriginatorWalletType.originatorLegalType']//div[contains(@class, 'travel-rule__beneficiary-type-item') and text() = 'Company']",
            )
            company_button.click()

            source_exchange_doc = modal_window.find_element(
                By.XPATH, "//div[@id='travelRuleoriginatorWalletType.vaspEntityId']"
            )
            source_exchange_select = source_exchange_doc.find_element(By.XPATH, ".//div[text()='Please select']")
            source_exchange_select.click()
            self.sleep(0.25, "Open exchange filter")
            source_exchange_doc.find_element(By.XPATH, ".//input").send_keys(SOURCE_EXCHANE_NAME[source_exchange])
            self.sleep(2.0, "Filter update for exchange")
            input_box = source_exchange_doc.find_element(
                By.XPATH,
                f".//div[contains(@class, 'travel-rule__select-item') and text() = '{SOURCE_EXCHANE_NAME[source_exchange]}']",
            )
            input_box.click()
            self.sleep(0.5, "Exchange filter close")

            company_name = modal_window.find_element(
                By.XPATH,
                ".//div[@id='travelRuleoriginatorWalletType.originatorLegalType.originatorFirstName']//input",
            )
            company_name.send_keys(COMPANY_NAME)

            document_type = modal_window.find_element(
                By.XPATH,
                ".//div[@id='travelRuleoriginatorWalletType.originatorLegalType.originatorPoiType']",
            )
            document_select = document_type.find_element(By.XPATH, ".//div[text()='Please select']")
            document_select.click()
            document_type.find_element(By.XPATH, ".//input").send_keys("Business")
            self.sleep(0.25, "Filter update for document type")

            input_box = document_type.find_element(
                By.XPATH,
                ".//div[contains(@class, 'travel-rule__select-item') and text() = 'Business identification number']",
            )
            input_box.click()

            document_number = modal_window.find_element(
                By.XPATH,
                ".//div[@id='travelRuleoriginatorWalletType.originatorLegalType.originatorPoiNumber']//input",
            )
            document_number.send_keys(COMPANY_NUMBER)

            document_country = modal_window.find_element(
                By.XPATH,
                ".//div[@id='travelRuleoriginatorWalletType.originatorLegalType.originatorPoiIssuingCountry']",
            )
            document_country_select = document_country.find_element(By.XPATH, ".//div[text()='Please select']")
            document_country_select.click()
            self.sleep(0.25, "Open country filter")
            document_country.find_element(By.XPATH, ".//input").send_keys("Turkey")
            self.sleep(0.25, "Filter update for country")
            input_box = document_country.find_element(
                By.XPATH,
                ".//div[contains(@class, 'travel-rule__select-item') and text() = 'Turkey']",
            )
            input_box.click()
            self.sleep(0.25, "Country filter close")

            modal_window.find_element(By.XPATH, "//button[text() = 'Submit']").click()
            self.sleep(3, "Submit details")

            modal_confirm_window = self.driver.find_element(
                By.XPATH, "//div[contains(@class, 'moly-modal')]//button[text() = 'Confirm']"
            )
            modal_confirm_window.click()
        except Exception as e:
            self._mon_errors.inc()
            log.error(f"Failed to populate details {traceback.format_exc()}")
            self.driver.get(DEPOSIT_HISTORY)
        finally:
            self.sleep(5, "deposit window reload after detail submission")

    def open_declaration_form(self, row_id):
        current_handles = set(self.driver.window_handles)
        populate_details_button = self.driver.find_element(
            By.XPATH, f"//tbody/tr[@data-row-key = '{row_id}']/following-sibling::tr//button"
        )
        populate_details_button.click()
        self.sleep(1, "Declaration link modal window open")
        self.driver.find_element(By.XPATH, "//div[contains(@class, 'travel-rule__verification-button')]").click()
        self.sleep(1, "Declaration link tab open")

        new_handles = set(self.driver.window_handles)
        new_tabs = new_handles.difference(current_handles)
        if len(new_tabs) != 1:
            raise Exception("Unable to find declaration form window")

        self.driver.switch_to.window(next(iter(new_tabs)))
        self.sleep(5, "Deposit declaration form loading")

    def verify_wallet_info(self, other_exchange):

        self.sleep(2, "Open self declaration")
        self.driver.find_element(By.XPATH, f"//input[@id = 'v-0-0-2']").send_keys(COMPANY_NAME)
        self.driver.find_element(By.XPATH, f"//input[@id = 'v-0-0-5']").send_keys(EXCHANGE_MAPPING[other_exchange])
        self.driver.find_element(By.XPATH, f"//button[@id = 'v-0-0-8']").click()
        self.sleep(0.5, "Open national id dropdown")

    def populate_declaration(self, row_id, other_exchange):
        self.open_declaration_form(row_id)

        try:
            on_decl = len(self.driver.find_elements(By.XPATH, "//h1[text() = 'Transaction Declaration Form']")) > 0
            on_wallet = len(self.driver.find_elements(By.XPATH, "//h1[text() = 'Verify your wallet information']")) > 0

            log.debug(f"Declaration form for {row_id} has data use confirmation: {not on_decl and not on_wallet}")
            if not on_decl and not on_wallet:
                self.driver.find_element(By.XPATH, "//footer/button").click()
                self.sleep(2, "Declaration load")
                self.driver.find_element(By.XPATH, "//input[@type = 'checkbox']").click()
                self.sleep(0.5, "Click agree")
                self.driver.find_element(By.XPATH, "//footer/button").click()
                self.sleep(2, "Submit agree to declare")

            wallet_info = (
                on_wallet
                or len(self.driver.find_elements(By.XPATH, "//h1[text()='Verify your wallet information']")) > 0
            )

            if wallet_info:
                log.debug("Found headline for wallet info, verifying wallet info")
                self.driver.find_element(By.XPATH, "//button[normalize-space()='Self-declaration']").click()
                self.sleep(2, "Open supplementary decl form")

            self.driver.find_element(By.XPATH, f"//input[@placeholder = 'Counterparty name']").send_keys(COMPANY_NAME)

            other_wallet_name = "Digital wallet provider name" if on_wallet else "Crypto asset service provider name"
            self.driver.find_element(By.XPATH, f"//input[@placeholder = '{other_wallet_name}']").send_keys(
                EXCHANGE_MAPPING[other_exchange]
            )
            self.driver.find_element(By.XPATH, f"//span[contains(@label, 'National ID Number')]/button").click()
            self.sleep(0.25, "Declaration document type")
            if wallet_info:
                self.driver.find_element(
                    By.XPATH,
                    "//button[contains(@class, 'RadioCheckContainer') and contains(normalize-space(),'Business Identification Number')]",
                ).click()
            else:
                self.driver.find_element(
                    By.XPATH,
                    "//button[contains(@class, 'RadioCheckContainer') and contains(normalize-space(),'Tax Number of Sender')]",
                ).click()
            self.sleep(0.25, "Return to declaration from document type")
            if wallet_info:
                self.driver.find_element(
                    By.XPATH, f"//input[@placeholder = 'Business Identification Number']"
                ).send_keys(COMPANY_NUMBER)
            else:
                self.driver.find_element(By.XPATH, f"//input[@placeholder = 'Tax number']").send_keys(TAX_NUMBER)
            if not wallet_info:
                self.driver.find_element(
                    By.XPATH, f"//textarea[@placeholder = 'Specify the transaction purpose']"
                ).send_keys(DEPOSIT_REASON)
            self.driver.find_element(By.XPATH, "//footer//button[normalize-space() = 'Continue']").click()
            self.sleep(1, "Waiting for declaration sendout")
        finally:
            self.driver.close()
            self.driver.switch_to.window(self.bybit_tab)
            self.sleep(0.5, "Switch back to bybit tab")
            self.driver.find_element(By.CLASS_NAME, "travel-rule__verification").send_keys(Keys.ESCAPE)
            self.sleep(1, "Wait for declaration result modal to close")

    def process_deposits(self):
        self.driver.get(DEPOSIT_HISTORY)
        self.sleep(10, "deposit load")

        PAGE_HISTORY = 2
        total_details = 0
        total_decls = 0
        try:
            for _ in range(PAGE_HISTORY):
                details_required = []
                declaration_needed = []

                for elem in self.driver.find_elements(
                    By.XPATH, "//tbody/tr[contains(@class, 'funding-records__common-table-row')]"
                ):
                    status = elem.find_element(By.XPATH, ".//div[contains(@class, 'records__deposit-status')]").text
                    if status == "Details required":
                        details_required.append(elem.get_attribute("data-row-key"))
                    elif status == "Declaration needed":
                        declaration_needed.append(elem.get_attribute("data-row-key"))
                    elif status == "Wallet verification needed":
                        declaration_needed.append(elem.get_attribute("data-row-key"))

                total_details += len(details_required)
                total_decls += len(declaration_needed)

                log.info(
                    f"Details required for = {len(details_required)}, declarations needed for = {len(declaration_needed)}"
                )

                for row_id in details_required:
                    log.debug(f"Looking for {row_id}")
                    data_row = self.driver.find_element(By.XPATH, f"//tbody/tr[@data-row-key = '{row_id}']")
                    transaction = self.get_transaction_details(data_row)
                    source_exchange = self.trx_dao.get_deposit_source_exchange(transaction)
                    log.debug(f"Source exchange for deposit: {source_exchange}")
                    if source_exchange is None:
                        log.info(f"Unable to find transfer for deposit {transaction} (row_id={row_id})")
                        continue
                    self.populate_deposit_details(row_id, source_exchange)
                    self._mon_deposit_details_provided.inc()

                for row_id in declaration_needed:
                    log.debug(f"Looking for {row_id}")
                    data_row = self.driver.find_element(By.XPATH, f"//tbody/tr[@data-row-key = '{row_id}']")
                    transaction = self.get_transaction_details(data_row)
                    source_exchange = self.trx_dao.get_deposit_source_exchange(transaction)
                    if source_exchange is None:
                        log.info(f"Unable to find transfer for deposit {transaction} (row_id={row_id})")
                        continue
                    try:
                        self.populate_declaration(row_id, source_exchange)
                        self._mon_deposit_decl_provided.inc()
                    except Exception:
                        self._mon_errors.inc()
                        log.error(f"Failed to populate declaration: {traceback.format_exc()}")
                        self.driver.switch_to.window(self.bybit_tab)
                        self.driver.get(DEPOSIT_HISTORY)
                    finally:
                        self.sleep(5, "reload deposit window")
                self.driver.find_element(
                    By.XPATH,
                    "//button[contains(@class, 'funding-records__pagination-btn-item') and normalize-space()='Next']",
                ).click()
                self.sleep(5, "Load next deposit page")
        finally:
            self._mon_deposit_details_req.set(total_details)
            self._mon_deposit_decl_req.set(total_decls)

    def process_withdrawals(self):
        log.info("Processing withdrawals")
        self.driver.get(WITHDRAW_HISTORY)
        self.sleep(10, "Load withdrawal history")
        declaration_needed = []
        for elem in self.driver.find_elements(
            By.XPATH, "//tbody/tr[contains(@class, 'funding-records__common-table-row')]"
        ):
            status = elem.find_element(By.XPATH, ".//div[contains(@class, 'records-withdraw__status')]").text
            if status == "Declaration needed":
                declaration_needed.append(elem.get_attribute("data-row-key"))
            elif status == "Wallet verification needed":
                declaration_needed.append(elem.get_attribute("data-row-key"))

        self._mon_withdrawal_decl_req.set(len(declaration_needed))

        for row_id in declaration_needed:
            log.debug(f"Looking for {row_id}")
            data_row = self.driver.find_element(By.XPATH, f"//tbody/tr[@data-row-key = '{row_id}']")
            transaction = self.get_transaction_details(data_row)
            target_exchange = self.trx_dao.get_withdrawal_target_exchange(transaction)
            if target_exchange is None:
                log.info(f"Unable to find transfer for withdrawal {transaction} (row_id={row_id})")
                continue
            try:
                self.populate_declaration(row_id, target_exchange)
                self._mon_withdrawal_decl_provided.inc()
            except Exception:
                self._mon_errors.inc()
                log.error(f"Failed to populate declaration: {traceback.format_exc()}")
                self.driver.switch_to.window(self.bybit_tab)
                self.driver.get(WITHDRAW_HISTORY)
            finally:
                self.sleep(5, "reload withdrawal window")

    def run(self):

        while True:
            try:
                self.switch_to_bybit_tab()
                self.check_login_status()
                self.process_deposits()
                self.process_withdrawals()
                self.driver.refresh()
                self._mon_loop_end.set_to_current_time()
            except Exception:
                self._mon_errors.inc()
                log.error(f"Failed to click: {traceback.format_exc()}")

            self.sleep(60, "end of loop")


def main():
    main_app(BybitTrClicker)


if __name__ == "__main__":
    main()
