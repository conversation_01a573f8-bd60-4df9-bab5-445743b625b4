import asyncio
import os
from datetime import datetime, timed<PERSON>ta
from typing import <PERSON><PERSON>
from ._rebate import Rebate
from .metrics import Metrics
from .slack import Slack
from .config import log

def do_i_need_to_run(old_date: datetime):
    #  target of this is to run once a month
    today = datetime.now()
    if today.day == 1 and old_date.month != today.month and today.hour > 3:
        return True
    return False


def get_last_month_timestamps() -> <PERSON><PERSON>[datetime, datetime]:
    today = datetime.now()
    first_day_of_current_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_day_of_last_month = first_day_of_current_month - timedelta(seconds=1)
    first_day_of_last_month = last_day_of_last_month.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    return first_day_of_last_month, last_day_of_last_month,


async def sentry_mode(config, output_dir, loop):
    slack = Slack(config["slack"]["token"], config["slack"]["channel_id"])
    old_date = datetime.now()
    fail_count = 0
    slack.send_message("Rebate script is running!")
    while True:
        if do_i_need_to_run(old_date):
            try:
                Metrics.CURRENTLY_CALCULATING.set(1)
                rebate = Rebate(loop)
                await rebate.async_init(config)
                rebate.old_date = datetime.now()
                # time interval is 1 month, from the first day of the month
                start_date, end_date = get_last_month_timestamps()
                slack.send_message("Rebate report is generating for " + start_date.strftime("%Y-%m-%d") + " to " + end_date.strftime("%Y-%m-%d"))
                log.info("Generating rebate report for ", start_date, " to ", end_date)
                await rebate.process_trades(start_date.timestamp(), end_date.timestamp())
                files = rebate.write_rebate(output_dir)
                send_to_slack(config, files, start_date, end_date)
                slack.send_message(f"Rebate report generated!")
                old_date = datetime.now()
                Metrics.LAST_SUCCESSFUL_CALCULATION_TIME.set_to_current_time()
                Metrics.CURRENTLY_CALCULATING.set(0)
                fail_count = 0
                Metrics.FAIL_COUNT.set(fail_count)
            except Exception as e:
                slack.send_message(f"Rebase report failed with {e}")
                fail_count += 1
                Metrics.CURRENTLY_CALCULATING.set(0)
                Metrics.FAIL_COUNT.set(fail_count)
        await asyncio.sleep(60 * 60 * 2)

def send_to_slack(config: dict, files: Tuple, start_date: datetime, end_date: datetime):
    slack = Slack(config["slack"]["token"], config["slack"]["channel_id"])
    for f in files:
        slack.send_file(f, f"This is rebate report for {start_date} to {end_date} in {os.path.basename(f)}")
