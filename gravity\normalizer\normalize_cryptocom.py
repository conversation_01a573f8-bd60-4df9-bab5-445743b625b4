from datetime import datetime
from typing import Optional

from gravity.normalizer.normal import Normalizer, Transfer, Deposits, Withdrawals


class CryptocomNormalizer(Normalizer):
    exchange = "cryptocom"

    def normalize_row(self, row, file_name) -> Optional[Transfer]:
        asset = row["asset"]
        ts = float(row["requested_ts"])
        txid = row["txid"]
        amount = float(row["amount"])
        address = row["address"]
        memo = row["memo"]

        if "deposits" in file_name:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=memo,
                txid=txid,

            )
        elif "withdrawals" in file_name:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=memo,
                txid=txid,
            )