import time
from typing import List, AnyStr
from os import environ, path
from setuptools import setup


MODULE_NAME = "cyvers"


def install_requires() -> List[AnyStr]:
    """
    Dependency fetched

    :return: dependencies ar list of strings
    """
    with open(path.join(path.dirname(path.realpath(__file__)), "requirements.txt"), "rt") as handle:
        req = handle.readlines()
    return req


setup(
    name=f"gt-{MODULE_NAME}",
    version=environ.get("PKG_VER", "1.0.1"),
    description="Cyvers alert proxy",
    author="arb-bot-devs",
    platforms="linux-x86_64",
    zip_safe=True,
    python_requires=">=3.8",
    long_description="commit:{}".format(environ.get("CI_COMMIT_SHA", "manual-build")),
    package_dir={"gravity.{}".format(MODULE_NAME): "./src"},
    classifiers=[
        "Private :: Do Not Upload",
        "Gravity Team :: Git :: Repository :: {}".format(environ.get("CI_PROJECT_PATH", "local build")),
        "Gravity Team :: Git :: Branch :: {}".format(environ.get("CI_COMMIT_REF_SLUG", "unknown")),
        "Gravity Team :: Git :: Commit :: {}".format(environ.get("CI_COMMIT_SHA", "unknown")),
        "Gravity Team :: Git :: Time :: {}".format(environ.get("CI_COMMIT_TIMESTAMP", "unknown")),
        "Gravity Team :: Git :: Author :: {}".format(environ.get("CI_COMMIT_AUTHOR", "local build")),
        "Gravity Team :: CI :: ID :: {}/{}".format(environ.get("CI_PIPELINE_ID", "0"), environ.get("CI_JOB_ID", "0")),
        "Gravity Team :: CI :: Time :: {}".format(time.time()),
    ],
    install_requires=install_requires(),
    entry_points={"console_scripts": [f"{MODULE_NAME}=gravity.{MODULE_NAME}.main:main"]},
)
