import sys
import os.path
import logging
import logging.handlers
from typing import Dict
from argparse import ArgumentParser
from yaml import safe_load

from . import ctx


PROM_NS = "gt_cyvers"


class Configuration:
    """
    Stuff related to configuration handling
    """

    def __init__(self, app_name: str):
        self.log = logging.getLogger(self.__class__.__name__)
        self.app_name = app_name
        self.args = None  # CLI parameters - should be immutable object
        self.yml: Dict = {}  # Trimmed stuff read from configuration file, mutable

    def argparse(self) -> None:
        """
        CLI param handler

        :return: None
        """
        parser = ArgumentParser(description="Gravity Team Cyvers alert proxy")
        parser.add_argument(
            "-bind-port",
            type=int,
            default=9120,
            required=False,
            dest="port",
            help="Port to which process should bind",
        )
        parser.add_argument(
            "-bind-addr",
            type=str,
            default="0.0.0.0",
            required=False,
            dest="addr",
            help="Address to which process should bind",
        )
        parser.add_argument(
            "-config-file",
            type=str,
            default="config.yml",
            required=True,
            dest="config_file",
            help="Configuration file location",
        )
        parser.add_argument(
            "-log-dir",
            type=str,
            default="logs",
            required=False,
            dest="log_dir",
            help="Log file location",
        )
        parser.add_argument(
            "-log-stdout",
            default=False,
            required=False,
            action="store_true",
            dest="log_stdout",
            help="Log to stdout instead of file",
        )
        parser.add_argument(
            "-data-dir",
            type=str,
            default="data",
            required=False,
            dest="data_dir",
            help="Alert data location",
        )
        self.args = parser.parse_args()

    def setup_logging(self) -> None:
        """
        Logging setup.

        Semi sane logging setup.
        :return: None
        """
        if not os.path.exists(os.path.abspath(self.args.log_dir)):
            os.makedirs(os.path.abspath(self.args.log_dir))

        if self.args.log_stdout:
            handler = logging.StreamHandler(stream=sys.stdout)
        else:
            handler = logging.handlers.TimedRotatingFileHandler(
                filename=f"{str(os.path.abspath(os.path.join(self.args.log_dir, self.app_name.lower())))}.log",
                when="D",
                backupCount=10,
                utc=True,
                delay=False,
            )
        handler.addFilter(TidFilter())
        logging.basicConfig(
            level=logging.INFO,
            handlers=[handler],
            format="%(asctime)s:%(levelname)s:%(tid)s:%(name)s.%(funcName)s:%(message)s",
            datefmt="%Y-%m-%dT%H:%M:%S%z",
        )

    def config_loader(self) -> None:
        """
        Mutable config loader.

        Parses configuration file
        :return: None
        """
        self.log.info(f"Loading configuration from {os.path.abspath(self.args.config_file)}")
        try:
            with open(os.path.abspath(self.args.config_file)) as f:
                cfg = safe_load(stream=f)
            if self.app_name.lower() not in cfg.keys():
                self.log.error(f"Invalid configuration")
                sys.exit(2)
            else:
                self.yml = cfg.get(self.app_name.lower())
                # TODO: add configuration validation
                self.log.info("Configuration loaded")
        except FileNotFoundError as e:
            self.log.error("Unable to load configuration file", exc_info=e)
            sys.exit(1)

    @staticmethod
    def request_logger() -> logging.Logger:
        logger = logging.getLogger("requests")
        log_file = str(os.path.abspath(os.path.join(ctx.ctx.cfg.args.log_dir, "request.log")))
        handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_file,
            when="D",
            backupCount=10,
            utc=True,
            delay=False,
        )
        handler.setFormatter(logging.Formatter(fmt="%(asctime)s:%(levelname)s:%(tid)s:%(message)s"))
        handler.addFilter(TidFilter())
        logger.addHandler(handler)
        logger.propagate = False
        return logger


class TidFilter(logging.Filter):
    def filter(self, record):
        if ctx.ctx is not None and ctx.ctx.tid is not None:
            record.tid = ctx.ctx.tid.get()
        else:
            record.tid = 900000
        return True
