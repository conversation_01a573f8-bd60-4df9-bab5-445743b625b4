# Cyverse

Cyverse is a service that calls users when a webhook is received.

## Installation
```bash
pip install gt-cyverse
```

## Usage
```bash
python -m cyverse -c <config_file> -s <server_port> -h <server_host> -b <bind_port> -B <bind_host>
```
- config_file: config file with call priority list
- server_port: port used by flask api for cyverse webhook and twilio callback
- server_host: host used by flask api for cyverse webhook and twilio callback
- bind_port: port used by promethium
- bind_host: host used by promethium

## Config file
config file is a yaml file with the following structure:

```json
{
    "twilio_account_sid": "<twilio_account_sid>",
    "twilio_auth_token": "<twilio_auth_token>",
    "twilio_phone_number": "<twilio_phone_number>",
    "callback_url": "<callback_url>",
    "phone_numbers": [
       {"<name>": "+<phone_number>"},
       {"<name>": "+<phone_number>"},
       
    ]
}