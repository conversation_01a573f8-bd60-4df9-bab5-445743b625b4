from prometheus_client import Gauge

class Metrics:
    ns = "gt_rebate_calculator"
    # Monitoring echo systems sanity
    LAST_SUCCESSFUL_CALCULATION_TIME = Gauge(namespace=ns, name="cycle_ts", documentation="Last cycles completion time")
    CURRENTLY_CALCULATING = Gauge(namespace=ns, name="current_calculating", documentation="Currently calculating")
    FAIL_COUNT = Gauge(namespace=ns, name="fail_count", documentation="Fail count")