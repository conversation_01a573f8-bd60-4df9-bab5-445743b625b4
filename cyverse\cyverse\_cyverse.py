import asyncio
import time

from flask import Flask, request, Response
import logging

from .metrics import Metrics
from .call import Caller, format_message
log = logging.getLogger(__name__)


def access_log():
    log.info("{}|{}|{}|{}".format(request.method, request.url, request.remote_addr, request.user_agent))


def parse_data(data):
    id = data['id']
    name = data.get("action", {}).get("name")
    entry = data.get("entity", [])
    if isinstance(entry, list):
        entry = entry[0]
    category_name = entry.get("category_name")
    entity_name = entry.get("name")
    return id, name, category_name, entity_name


class Cyverse():
    def __init__(self, account_sid, auth_token, phone_number, callback_url, phone_numbers, server_port, server_host):
        self.twellio = Caller(account_sid, auth_token, phone_number, callback_url)
        self.phone_numbers = phone_numbers
        self.app = Flask(__name__)
        self.setup_routes()
        self.call_queue = {}
        self.loop = asyncio.get_event_loop()
        self.server_port = server_port
        self.server_host = server_host

    def setup_routes(self):
        @self.app.route("/status", methods=["GET"])
        async def status():
            access_log()
            return "ok"

        @self.app.route('/hp', methods=['GET'])
        async def health():
            access_log()
            return Response(status=200)

        @self.app.route('/<seed>/health', methods=['GET'])
        async def health_v2(seed):
            access_log()
            return Response(status=200)

        @self.app.route('/<seed>/webhook', methods=['POST'])
        async def webhook(seed):
            id, name, category_name, entity_name = parse_data(request.json)
            message = format_message(name, category_name, entity_name)
            Metrics.LAST_ERROR_TIME.set_to_current_time()
            msg = self.make_a_call(message)
            self.call_queue[msg.sid] = [message, time.time(), 0, 0]
            return Response(status=200)

        @self.app.route('/<path:id>/callback', methods=['POST', 'GET'])
        async def callback(id):
            try:
                call_sid = request.form.get('CallSid')
                call_status = request.form.get('CallStatus')
                print(call_status)
                if call_sid in self.call_queue:
                    if call_status == 'completed':
                        del self.call_queue[call_sid]
                    elif call_status in ['busy', 'no-answer']:
                        self.call_queue[call_sid][3] += 1
                        print(self.call_queue[call_sid])
                        if self.call_queue[call_sid][3] < 3:
                            await self.wait_call(call_sid)
                        else:
                            self.call_queue[call_sid][2] += 1
                            if self.call_queue[call_sid][2] >= len(self.phone_numbers):
                                log.info(f"No more phone numbers to call, no one is available")
                                return '', 200
                            self.call_queue[call_sid][3] = 0
                            await self.wait_call(call_sid)

                log.info(f"Call {call_sid} status: {call_status}")
                return '', 200
            except Exception as e:
                log.error(e)
                return '', 400

    async def wait_call(self, old_call_sid):
        await asyncio.sleep(10)
        msg = self.make_a_call(self.call_queue[old_call_sid][2])
        self.call_queue[msg.sid] = self.call_queue.pop(old_call_sid)

    def make_a_call(self, msg, priority=0):
        to = self.phone_numbers[priority]
        to = list(to.values())[0]
        Metrics.CALLER_COUNT.inc()
        Metrics.LAST_CALL_TIME.set_to_current_time()
        return self.twellio.make_a_call(to, msg)


    def run(self):
        self.app.run(host=self.server_host, port=self.server_port, debug=False)
