from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class MercadoNormalizer(Normalizer):
    exchange = "mercado"

    def normalize_row(self, row, file_name):
        # 2022-10-20T17:04:22.893721
        time_format = "%d/%m/%Y %H:%M:%S"
        ts = datetime.strptime(row["Data"], time_format).timestamp()

        asset=row["Moeda"]
        amount = float(row["volume_ativo"].replace(",", "."))
        txid = str(row.get("tx"))

        if "WALLET-IN" in row["operation_type"]:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=txid or None,
            )
        elif "WALLET-OUT" in row["operation_type"]:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=row["destination_address"],
                tag=None,
                txid=txid or None,
            )