import math

from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals
from gravity.normalizer.normalize_bitbank import asset_map

asset_map = {
    "USD": "USDC"
}

BAD_TX_ASSETS = [
    "USDT"
]

TZ_SKEW = 60 * 60 * 3

class BitsoNormalizer(Normalizer):
    exchange = "bitso"
    skip_rows = 8

    def normalize_row(self, row, file_name: str):
        asset = asset_map.get(row["Currency"].upper(), row["Currency"].upper())

        if row["Status"] != "Complete":
            return None

        ts = float(row["Timestamp"]) - TZ_SKEW
        ts = math.ceil(ts / 1000)

        if "DEPOSITS" in file_name:
            return Deposits(
                asset=asset,
                amount=float(row["Gross Amount"]),
                ts=ts,
                exchange=self.exchange,
                fee=float(row["Fee"]),
                address=None,
                tag=None,
                txid=row["Deposit ID"] if asset not in BAD_TX_ASSETS else None,
            )
        elif "WITHDRAWALS" in file_name:
            return Withdrawals(
                asset=asset,
                amount=float(row["Gross Amount"]),
                ts=ts,
                exchange=self.exchange,
                fee=float(row["Fee"]),
                address=row["Receiver's Address"],
                tag=None,
                txid=row["Transaction ID"],
            )
