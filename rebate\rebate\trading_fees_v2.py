import asyncio
import collections
import copy
import sys
import time
from typing import Dict, List, Optional, NewType

from .timeperiod import TimePeriod
from ._types import Quote, Asset
import asyncpg

RELOAD_INTERVAL = 60 * 60
DEFAULT = "default"

PeriodData = NewType("PeriodFees", Dict[Quote, Dict[Asset, Dict]])


class TradingFeesV2:
    tablename: str = "unified.unified_trading_fees_v2"

    def __init__(self,  loop: asyncio.AbstractEventLoop , historical: bool = False, pg_pool: asyncpg.Pool = None):
        self.pg_pool = pg_pool
        self._historical_fees: Dict[str, Dict[TimePeriod, PeriodData]] = collections.defaultdict(dict)
        self._current_fees: Dict[str, PeriodData] = collections.defaultdict(lambda: collections.defaultdict(dict))
        self._cache: Dict[str, Dict] = collections.defaultdict(lambda: collections.defaultdict(dict))
        self._async_init_called: bool = False
        self.loop = loop

    def get(
        self, exchange: str, quote: Quote = DEFAULT, asset: Asset = DEFAULT, timestamp: Optional[float] = None
    ) -> Optional[Dict[str, float]]:
        return self._get_historical_fees(exchange, quote, asset, timestamp)

    def _get_historical_fees(
        self, exchange: str, quote: Quote, asset: Asset, timestamp: float
    ) -> Optional[Dict[str, float]]:
        for period, fees in self._historical_fees[exchange].items():
            if period.is_within(timestamp):
                return self._read_fees(fees, quote, asset)
        return None

    @staticmethod
    def _read_fees(fees: Dict, quote: Quote, asset: Asset) -> Optional[Dict[str, float]]:
        if fees[quote].get(asset):
            return fees[quote][asset]
        if fees[quote].get(DEFAULT):
            return fees[quote][DEFAULT]
        if fees[DEFAULT]:
            return fees[DEFAULT][DEFAULT]

    async def load(self):
        rows = await self._get_data()
        self._build_historical_fees(rows)

    def _sort_by_exchange(self, rows: List) -> Dict[str, List]:
        by_exchange: Dict[str, List] = collections.defaultdict(list)
        for row in rows:
            by_exchange[row["exchange"]].append(row)
        return by_exchange

    def _build_historical_fees(self, rows: List):
        _fees: Dict[str, Dict[TimePeriod, PeriodData]] = collections.defaultdict(dict)
        by_exchange = self._sort_by_exchange(rows)
        for exchange, rows in by_exchange.items():
            _current = collections.defaultdict(lambda: collections.defaultdict(dict))
            time_from = 0.0
            for row in rows:
                if float(row["time_from"]) > time_from:
                    time_to = float(row["time_from"])
                    _fees[exchange][TimePeriod(time_from, time_to)] = copy.deepcopy(_current)

                quote = Quote(row["quote"]) if row["quote"] else DEFAULT
                asset = Asset(row["base"]) if row["base"] else DEFAULT
                _current[quote][asset] = {"maker": float(row["maker"]), "taker": float(row["taker"])}
                time_from = float(row["time_from"])
            _fees[exchange][TimePeriod(time_from, sys.maxsize)] = copy.deepcopy(_current)
        self._historical_fees = _fees

    @staticmethod
    def _create_key(exchange: str, quote: Quote, asset: Asset) -> str:
        return f"{exchange}_{quote}_{asset}"

    async def _get_data(self) -> List:
        print("trading fees v2")
        conn =  await self.pg_pool.acquire()
        try:
            return await conn.fetch(
                "SELECT EXTRACT(epoch FROM timestamp_from)::NUMERIC AS time_from, gt_source, base, quote, maker, taker, exchange "
                f"FROM {self.tablename} "
                "WHERE market = 'spot' "
                "ORDER BY exchange, timestamp_from ASC")
        finally:
            await self.pg_pool.release(conn)

    async def get_exchanges_with_rebate(self, end_time: float):
        conn =  await self.pg_pool.acquire()
        try:
            return await conn.fetch(
                "SELECT exchange FROM unified.unified_trading_fees_v2 WHERE maker < 0 time_from <= $1 ORDER BY exchange, time_from ASC",
                end_time,
            )
        finally:
            await self.pg_pool.release(conn)

    async def get_exchanges_that_had_rebate(self, start_time: float, end_time: float):
        fees = await self._get_data()
        exchanges = []
        for fee in fees:
            if (fee["time_from"] > start_time or fee["time_from"] < end_time ) and float(fee["maker"]) > 0 and fee["exchange"] not in exchanges:
                exchanges.append(fee["exchange"])
        return exchanges


def output(fees):
    print("indodax", fees.get("indodax"))
    print("bitso BRL", fees.get("bitso", "BRL"))
    print("bitso ARS", fees.get("bitso", "ARS"))
    print("bitso BRL", fees.get("bitso", "BRL"))
    print("historic")
    print("bitso now", fees.get("bitso", quote="BRL", timestamp=time.time()))
    print("bitso -100day", fees.get("bitso", quote="BRL", timestamp=time.time() - 3600 * 24 * 100))
    print("bitso -7day", fees.get("bitso", timestamp=time.time() - 3600 * 24 * 7))


async def fees_test():
    loop = asyncio.get_event_loop()
    fees = TradingFeesV2(loop)
    await fees.load()
    i = 0
    while i < 8:
        await asyncio.sleep(4)
        output(fees)
        i += 1



if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(fees_test())
    asyncio.get_event_loop().run_forever()
