from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class BinanceUSNormalizer(Normalizer):
    exchange = "binanceus"

    def normalize_row(self, row,  file_name: str):
        time_format = "%Y-%m-%d %H:%M:%S.%f"
        if "deposit" in file_name.lower():
            return Deposits(
                asset=row['coin'],
                amount=float(row['transfer_amount']),
                ts=float(datetime.strptime(row["insert_time"], time_format).timestamp()),
                exchange=self.exchange,
                fee=None,
                address=row["target_address"],
                tag = row.get("target_address_tag"),
                txid=row["tx_id"]
            )
        elif "withdrawal" in file_name.lower():
            return Withdrawals(
                asset=row['coin'],
                amount=float(row['amount']),
                ts=float(datetime.strptime(row["apply_time"], time_format).timestamp()),
                exchange=self.exchange,
                fee=None,
                address=row["address"],
                tag=row.get("address_tag"),
                txid=row["tx_id"],
            )
        else:
            raise RuntimeError(f"Unknown file type in {self.exchange}: {file_name}")