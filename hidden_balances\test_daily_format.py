#!/usr/bin/env python3
"""
Test script to verify the daily column format functionality.
"""

import sys
import os
from decimal import Decimal
from datetime import datetime

# Add the balance_updater module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'balance_updater'))

from balance_updater.config import Config
from balance_updater.balance_aggregator import AccountBalance

def create_test_balances():
    """Create test balance data for verification."""
    test_balances = [
        AccountBalance(
            email="<EMAIL>",
            account_type="regular",
            spot_btc=Decimal("0.1"),
            spot_usdt=Decimal("1000.0"),
            margin_btc=Decimal("0.05"),
            margin_usdt=Decimal("500.0"),
            futures_btc=Decimal("0.02"),
            futures_usdt=Decimal("200.0"),
            total_btc=Decimal("0.17"),
            total_usdt=Decimal("1700.0")
        ),
        Account<PERSON>alance(
            email="<EMAIL>",
            account_type="managed",
            spot_btc=Decimal("0.2"),
            spot_usdt=Decimal("2000.0"),
            margin_btc=Decimal("0.1"),
            margin_usdt=Decimal("1000.0"),
            futures_btc=Decimal("0.05"),
            futures_usdt=Decimal("500.0"),
            total_btc=Decimal("0.35"),
            total_usdt=Decimal("3500.0")
        ),
        AccountBalance(
            email="<EMAIL>",
            account_type="regular",
            spot_btc=Decimal("0.0"),
            spot_usdt=Decimal("0.0"),
            margin_btc=Decimal("0.0"),
            margin_usdt=Decimal("0.0"),
            futures_btc=Decimal("0.0"),
            futures_usdt=Decimal("0.0"),
            total_btc=Decimal("0.0"),
            total_usdt=Decimal("0.0")
        )
    ]
    return test_balances

def test_config_loading():
    """Test configuration loading with new parameters."""
    print("Testing configuration loading...")
    
    try:
        config = Config(
            api_key="test_key",
            api_secret="test_secret",
            emails=["<EMAIL>"],
            google_sheets_id="test_sheet_id",
            google_credentials_path="test_creds.json",
            sheet_name="Test Sheet",
            use_daily_columns=True,
            date_format="%Y-%m-%d"
        )
        
        print(f"✓ Config loaded successfully")
        print(f"  - Use daily columns: {config.use_daily_columns}")
        print(f"  - Date format: {config.date_format}")
        print(f"  - Sheet name: {config.sheet_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ Config loading failed: {e}")
        return False

def test_balance_processing():
    """Test balance data processing."""
    print("\nTesting balance data processing...")
    
    try:
        balances = create_test_balances()
        
        # Test email extraction
        emails = set()
        for balance in balances:
            emails.add(balance.email)
        
        sorted_emails = sorted(list(emails))
        print(f"✓ Extracted {len(sorted_emails)} unique emails:")
        for email in sorted_emails:
            print(f"    - {email}")
        
        # Test balance mapping
        balance_map = {}
        for balance in balances:
            balance_map[balance.email] = float(balance.total_usdt)
        
        print(f"✓ Created balance mapping:")
        for email, usdt in balance_map.items():
            print(f"    - {email}: ${usdt:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Balance processing failed: {e}")
        return False

def test_date_formatting():
    """Test date formatting functionality."""
    print("\nTesting date formatting...")
    
    try:
        date_format = "%Y-%m-%d"
        today = datetime.now().strftime(date_format)
        print(f"✓ Today's date formatted as: {today}")
        
        # Test different formats
        formats = [
            "%Y-%m-%d",      # 2024-01-15
            "%m/%d/%Y",      # 01/15/2024
            "%d-%b-%Y",      # 15-Jan-2024
        ]
        
        for fmt in formats:
            formatted = datetime.now().strftime(fmt)
            print(f"  - Format '{fmt}': {formatted}")
        
        return True
        
    except Exception as e:
        print(f"✗ Date formatting failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING DAILY COLUMN FORMAT FUNCTIONALITY")
    print("=" * 60)
    
    tests = [
        test_config_loading,
        test_balance_processing,
        test_date_formatting,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The daily column format is ready to use.")
    else:
        print("✗ Some tests failed. Please check the implementation.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
