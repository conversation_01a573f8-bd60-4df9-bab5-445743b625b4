# Binance API Configuration
api_key: "YOUR_BINANCE_API_KEY"
api_secret: "YOUR_BINANCE_API_SECRET"

# Google Sheets configuration
google_sheets_id: null  # Replace with your Google Sheets ID (e.g., "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
google_credentials_path: "credentials.json"  # Path to Google service account credentials
sheet_name: "Hidden Balances_Ops"  # Name of the worksheet to update

# Balance tracking configuration
use_daily_columns: true  # Use daily column format for balance tracking
date_format: "%Y-%m-%d"  # Format for date headers (YYYY-MM-DD)
end_of_day_cutoff_hour: 6  # Hour (0-23) after which to use previous day's date for end-of-day balances

# Email filter - only process these accounts (leave empty to process all)
emails:
  # - <EMAIL>
  # - <EMAIL>
