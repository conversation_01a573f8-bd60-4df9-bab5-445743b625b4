from datetime import datetime

from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class ValrNormalizer(Normalizer):
    exchange = "valr"

    def normalize_row(self, row, file_name):
        time_format = "%Y-%m-%d %H:%M:%S Z"
        if "Receive" in row["transaction type"]:
            print(row)
            return Deposits(
                row["credit currency"].split("-")[0],
                abs(float(row["credit value"])),
                int(datetime.strptime(row["date"], time_format).timestamp()) ,
                self.exchange,
                float(row["fee value"]) if row["fee value"] else 0,
                row["address"],
                None,
                row["transactionHash"],
            )
        elif "Send" in row["transaction type"]:
            print(row)
            return Withdrawals(
                row["debit currency"].split("-")[0],
                abs(float(row["debit value"])),
                int(datetime.strptime(row["date"], time_format).timestamp()) - (60*60*3),
                self.exchange,
                float(row["fee value"]) if row["fee value"] else 0,
                row["address"],
                row.get("tag"),
                row["transactionHash"],
            )
