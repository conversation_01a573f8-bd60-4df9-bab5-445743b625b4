import math

from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

asset_map = {
"UST": "USDT"
}

def normalize_asset(asset):
    if asset in asset_map:
        return asset_map[asset]
    return asset

class BitfinexNormalizer(Normalizer):
    exchange = "bitfinex"

    def normalize_row(self, row, file_name):
        time_format = "%y-%m-%d %H:%M:%S"
        if row["STATUS"] != "COMPLETED":
            return
        
        asset = normalize_asset(row['CURRENCY'])
        ts = int(datetime.strptime(row["DATE"], time_format).timestamp())
        amount = float(row["AMOUNT"])
        fee = float(row["FEES"])
        txid = row["TRANSACTION ID"]
        address = row["DESCRIPTION"]

        if amount > 0:
            return Deposits(
                asset=asset,
                amount=abs(amount),
                ts=ts,
                exchange=self.exchange,
                fee=abs(fee),
                address=address,
                tag=None,
                txid=txid,
            )
        else:
            return Withdrawals(
                asset=asset,
                amount=abs(amount),
                ts=ts,
                exchange=self.exchange,
                fee=abs(fee),
                address=address,
                tag=None,
                txid=txid,
            )