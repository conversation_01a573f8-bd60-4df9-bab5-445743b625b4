from datetime import datetime, timezone

from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class IndodaxNormalizer(Normalizer):
    exchange = "indodax"

    def normalize_row(self, row, file_name):

        tx_id = row["tx"]

        tx_type = None
        asset = None

        if row["status"] == "deposit_idr":
            tx_type = "deposit"
            asset="IDR"
        if row["status"] == "withdraw_idr":
            tx_type = "withdraw"
            asset="IDR"
        else:
            try:
                tx_type, _, asset = row["status"].split("_")
            except:
                print(f"failed to parse {self.exchange} row")
                print(row)
                return None

        amount = 0 # can't really use total_in_idr value

        time_format = "%Y-%m-%d %H:%M:%S"
        ts = datetime.strptime(row["date_time"], time_format).replace(tzinfo=timezone.utc).timestamp()

        if "deposit" in tx_type:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=tx_id,
            )
        elif "withdraw" in tx_type:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=row["asset_address_or_bank_code"],
                tag=None,
                txid=tx_id
            )
