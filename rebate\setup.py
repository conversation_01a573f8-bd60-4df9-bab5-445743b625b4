from datetime import datetime
from os import environ
from typing import Any, List
from setuptools import setup

def deps_public() -> List[Any]:
    with open("requirements.txt") as f:
        return [x.strip() for x in f.readlines()]

setup(
    name="gt-rebate",
    version="0.0.41",
    description="Rebate calculator",
    author="Gravity",
    author_email="<EMAIL>",
    packages=["rebate"],
    install_requires=deps_public(),
    classifiers=[
        "Private :: Do Not Upload",
        "Gravity Team :: Git :: Repository :: {}".format(environ.get("CI_PROJECT_PATH", "local build")),
        "Gravity Team :: Git :: Branch :: {}".format(environ.get("CI_COMMIT_REF_SLUG", "unknown")),
        "Gravity Team :: Git :: Commit :: {}".format(environ.get("CI_COMMIT_SHA", "unknown")),
        "Gravity Team :: Git :: Time :: {}".format(environ.get("CI_COMMIT_TIMESTAMP", "unknown")),
        "Gravity Team :: Git :: Author :: {}".format(environ.get("CI_COMMIT_AUTHOR", "local build")),
        "Gravity Team :: CI :: ID :: {}/{}".format(environ.get("CI_PIPELINE_ID", "0"), environ.get("CI_JOB_ID", "0")),
        "Gravity Team :: CI :: Time :: {}".format(datetime.utcnow()),
    ],
)