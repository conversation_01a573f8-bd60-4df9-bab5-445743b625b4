import pytest
from cyverse.srv import cyverse


@pytest.mark.parametrize("data", [
    {'id': 'bb67ae9c-3e28-4f8c-83ee-0f343a365675', 'watchlist': {'id': 496, 'name': 'Gravity'},
     'action': {'id': 1125, 'name': 'TEST2'}, 'risk_type': 'Wallet access control', 'risk_category': 'Security',
     'attack_phase': 'Exploitation', 'severity': 'Low', 'detection_time': '2024-12-10T13:03:22+00:00', 'asset': None,
     'entity': [{'name': 'Indodax', 'category_name': 'Centralized Exchange'}], 'involved_addresses': [
        {'address': '******************************************', 'role': 'Attacker', 'label': '', 'cluster_id': '',
         'cluster_label': '', 'value_lost_usd': None},
        {'address': '******************************************', 'role': 'Victim', 'label': '', 'cluster_id': '',
         'cluster_label': '', 'value_lost_usd': 1000.0}], 'total_value_lost_usd': 1000.0, 'tokens_lost': None,
     'audit': None, 'transaction_details': {'chain_id': 1, 'chain_name': 'Ethereum Mainnet',
                                            'hash': '0x0000000000000000000000000000000000000000000000000000000000000000',
                                            'timestamp': '2024-12-10T13:03:22+00:00', 'block_number': 0,
                                            'status': 'Confirmed'}}
]
                         )
def test_get_asset(data):
    id, name, category_name, entity_name = cyverse.parse_data(data)
    assert id == "bb67ae9c-3e28-4f8c-83ee-0f343a365675"
    assert name == "TEST2"
    assert category_name == "Centralized Exchange"
    assert entity_name == "Indodax"
