from gravity.matcher.match_transfers import transfers_match
from gravity.normalizer.normal import Transfer
import pytest

def test_transfers_match():
    # FTM, 8374.93, 1695587989.0, kraken, 0.07,, , 0x22166679cbc9c58e2b9674c65c93b6466063b9436e3e7b3d3f0b7ab237f289a4
    withdrawal = Transfer.from_row(
        {
            "asset": "FTM",
            "amount": 8374.93,
            "ts": 1695587989.0,
            "exchange": "kraken",
            "fee": 0.07,
            "address": "",
            "tag": "",
            "txid": "0x22166679cbc9c58e2b9674c65c93b6466063b9436e3e7b3d3f0b7ab237f289a4",
        }
    )
    # FTM,8374.93,1695584540,binanceus,,,,
    deposit = Transfer.from_row(
        {
            "asset": "FTM",
            "amount": 8374.93,
            "ts": 1695594540,
            "exchange": "binanceus",
            "fee": 0.0,
            "address": "",
            "tag": "",
            "txid": "",
        }
    )
    assert transfers_match(withdrawal, deposit) == "AMOUNT_AND_TIMESTAMP_MATCH"

def test_transfers_match_different_amount():
    # SHIB,453460000.0,1680929836,gateio,,-,,0x74978a8451be1fd805640ee555f2abd4cb3f02f6c06a9ffa80db2bf0eb7bd1e5
    deposit = Transfer.from_row(
        {
            "asset": "SHIB",
            "amount": 453460000.0,
            "ts": 1680929836 + (60 * 60),
            "exchange": "gateio",
            "fee": 0.0,
            "address": "",
            "tag": "",
            "txid": "0x74978a8451be1fd805640ee555f2abd4cb3f02f6c06a9ffa80db2bf0eb7bd1e5",
        }
    )
    # SHIB,453460000.0,1680930066.0,valr,820000.0,0x8E101Ce0cD87c816dCfD4d02F7E21983901dEf94,,0x74978a8451be1fd805640ee555f2abd4cb3f02f6c06a9ffa80db2bf0eb7bd1e5
    withdrawal = Transfer.from_row(
        {
            "asset": "SHIB",
            "amount": 453460000.0,
            "ts": 1680930066.0,
            "exchange": "valr",
            "fee": 820000.0,
            "address": "0x8E101Ce0cD87c816dCfD4d02F7E21983901dEf94",
            "tag": "",
            "txid": "0x74978a8451be1fd805640ee555f2abd4cb3f02f6c06a9ffa80db2bf0eb7bd1e5",
        }
    )
    assert transfers_match(withdrawal, deposit) == "AMOUNT_AND_TIMESTAMP_MATCH"
def test_transfers_match_different_timestamp():
    # XRP,18489.9,1680140962,ireserve,0,,,5830F4FA2573DC6E0593FC8F4CB55C509DB3614016E2F03CC6370321308F1B9C
    deposit = Transfer.from_row(
        {
            "asset": "XRP",
            "amount": 18489.9,
            "ts": 1680140962,
            "exchange": "ireserve",
            "fee": 0.0,
            "address": "",
            "tag": "",
            "txid": "5830F4FA2573DC6E0593FC8F4CB55C509DB3614016E2F03CC6370321308F1B9C",
        }
    )
    # XRP,18489.9,1680130174.0,valr,0.056,r33hypJXDs47LVpmvta7hMW9pR8DYeBtkW,,5830F4FA2573DC6E0593FC8F4CB55C509DB3614016E2F03CC6370321308F1B9C
    withdrawal = Transfer.from_row(
        {
            "asset": "XRP",
            "amount": 18489.9,
            "ts": 1680130174.0,
            "exchange": "valr",
            "fee": 0.056,
            "address": "r33hypJXDs47LVpmvta7hMW9pR8DYeBtkW",
            "tag": "",
            "txid": "5830F4FA2573DC6E0593FC8F4CB55C509DB3614016E2F03CC6370321308F1B9C",
        }
    )
    assert transfers_match(withdrawal, deposit) == "TIMESTAMP_MATCH"

def test_merged_transfer_match():
    # OCEAN,10900.45,1690240328.0,kraken,0,,,0x527704d5957c47c589d9ab0ab56b2925ff982eefc23d3f57942be124f7027159
    deposit = Transfer.from_row(

    )
