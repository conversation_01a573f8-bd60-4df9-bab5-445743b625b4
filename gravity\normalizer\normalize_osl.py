from datetime import datetime

from gravity.normalizer.normal import Deposits, Withdrawals, Normalizer


class OslNormalizer(Normalizer):
    exchange = "osl"

    def normalize_row(self, row, file_name: str):
        amount = float(row["Amount"])

        time_format = "%Y-%m-%d %H:%M:%S"
        if amount > 0:
            return Deposits(
                row['Currency'],
                amount,
                int(datetime.strptime(row["Processed Date Time"], time_format).timestamp()),
                self.exchange,
                float(row["Fee"]),
                None,
                None,
                None,
            )
        else:
            return Withdrawals(
                row['Currency'],
                abs(amount),
                int(datetime.strptime(row["Processed Date Time"], time_format).timestamp()),
                self.exchange,
                float(row["Fee"]),
                None,
                None,
                None,
            )