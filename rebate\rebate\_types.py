import sys
from typing import (
    NewType,
    Tuple,
    List,
    Callable,
    TypeVar,
    Sequence,
    Generic,
    overload,
    Optional,
)

Asset = NewType("Asset", str)
Quote = Asset
Symbol = Tuple[Asset, Quote]
Network = NewType("Network", str)
SourceId = NewType("SourceId", int)
ExchangeName = NewType("ExchangeName", str)
"""Withdrawal route exchange1->exchange2->exchange3"""
Route = Tuple[ExchangeName, ...]
SourceRoute = List[SourceId]

class MinMax:
    def __init__(self, min_value: Optional[float] = None, max_value: Optional[float] = None):
        self.min: float = min_value or -sys.float_info.max
        self.max: float = max_value or sys.float_info.max
