import argparse

from gravity.matcher.address_book import AddressBook
from gravity.matcher.cap_withdrawals import CapWithdrawals
from gravity.matcher.match_transfers import TransferMatcher


def main():
    parser = argparse.ArgumentParser(
        "Match withdrawals to deposits and report unmatched withdrawals and deposits"
    )
    parser.add_argument(
        "-i",
        "--input",
        help="Path to directory with normalized deposits and withdrawals",
    )
    parser.add_argument("-o", "--output", help="Path to output file")
    parser.add_argument(
        "-c",
        "--capman-withdrawals",
        dest="capman",
        help="Path to capman withdrawal export (csv)",
    )

    parser.add_argument(
        "-d", "--depost-addresses", dest="book",
        help="Path to deposit addresses export directory"
    )

    parser.add_argument(
        "-a", "--asset", dest="asset",
        help="Specific asset to read"
    )

    parser.add_argument(
        "-s", "--start-timestamp", dest="start_timestamp",
        help="Starting timestamp", type=float
    )

    parser.add_argument(
        "-e", "--end-timestamp", dest="end_timestamp",
        help="End timestamp", type=float
    )

    parser.add_argument("-m", "--source_map", dest="source_map_path", help="Path to cap2 source map view export")
    parser.add_argument("-t", "--deposit-transactions", dest="deposit_transaction_path", help="Path to a capcap2 dir of database deposit exports", type=str)

    args = parser.parse_args()
    if not args.capman or not args.input or not args.output:
        parser.print_help()
        exit(1)

    cap_withdrawals = CapWithdrawals(args.capman)

    address_book = AddressBook()
    if args.book:
        address_book.load_addresses(args.book, args.source_map_path)

    lookup_asset_list = [args.asset] if args.asset else None
    master = TransferMatcher(args.input, args.output, cap_withdrawals, address_book)


    if args.deposit_transaction_path:
        master.load_confirmed_deposits(args.deposit_transaction_path)

    deposits, withdrawals = master.load_transfers(assets=lookup_asset_list)

    address_book.update_from_transfers(deposits, assets=lookup_asset_list)

    master.match_transfers(deposits, withdrawals, start_timestamp=args.start_timestamp, end_timestamp=args.end_timestamp)

if __name__ == "__main__":
    main()
