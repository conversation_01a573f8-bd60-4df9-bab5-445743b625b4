from __future__ import annotations

import yaml
from datetime import datetime, timedelta, timezone
from pathlib import Path
import polars as pl


def load_cfg(path: str | Path = "config.local.yml") -> dict:
    with open(path, "rt", encoding="utf-8") as fh:
        return yaml.safe_load(fh)


def pg_uri(cfg: dict) -> str:
    db = cfg["database"]
    return (
        f"postgresql://{db['user']}:{db['password']}"
        f"@{db['host']}:{db['port']}/{db['database']}"
    )


def load_targeted_data(uri: str, days: int = 30) -> dict[str, pl.LazyFrame]:
    """Load only the data needed with targeted queries."""
    
    days = int(days)

    cutoff_date = datetime.now(timezone.utc).replace(tzinfo=None) - timedelta(days=days)

    transfers_sql = f"""
        SELECT gt_asset, network, gt_source_from, gt_source_to, gt_fee_in_usdt
        FROM capman.transfers
        WHERE gt_timestamp > '{cutoff_date.isoformat()}'
          AND gt_status_id BETWEEN 200 AND 299
    """

    asset_info_sql = """
        SELECT gt_asset, network, gt_source, can_withdraw, can_deposit
        FROM capman.capman_asset_info
        WHERE can_withdraw = true OR can_deposit = true
    """

    sources_map_sql = "SELECT gt_source, exchange FROM capman.sources_map"

    withdrawal_fees_sql = f"""
        SELECT DISTINCT wf.gt_asset, wf.network, wf.gt_source, wf.gt_fee_in_usdt
        FROM capman.withdrawal_fees wf
        WHERE EXISTS (
            SELECT 1 FROM capman.transfers t
            WHERE t.gt_asset = wf.gt_asset
              AND t.gt_timestamp > '{cutoff_date.isoformat()}'
              AND t.gt_status_id BETWEEN 200 AND 299
        )
    """

    print("[✓] Loading targeted data from Postgres...")

    return {
        "transfers": pl.read_database_uri(transfers_sql, uri, engine="connectorx").lazy(),
        "asset_info": pl.read_database_uri(asset_info_sql, uri, engine="connectorx").lazy(),
        "sources_map": pl.read_database_uri(sources_map_sql, uri, engine="connectorx").lazy(),
        "withdrawal_fees": pl.read_database_uri(withdrawal_fees_sql, uri, engine="connectorx").lazy(),
    }


def find_route_savings_opportunities(uri: str, days: int = 30, min_savings: float = 100.0) -> pl.LazyFrame:
    """Execute SQL-based query for multi-network analysis."""
    
    days = int(days)
    min_savings = float(min_savings)

    sql_query = f"""
    WITH transfer_vol AS (
      SELECT gt_asset, network, gt_source_from, gt_source_to,
             COUNT(*) AS transfer_count,
             SUM(gt_fee_in_usdt) AS fee_paid_usdt
      FROM capman.transfers
      WHERE gt_Timestamp > NOW() - INTERVAL '{days} days'
        AND gt_status_id >= 200 AND gt_status_id < 300
      GROUP BY gt_asset, network, gt_source_from, gt_source_to
    ),
    avg_fees AS (
      SELECT gt_asset, network,
             AVG(gt_fee_in_usdt) AS avg_fee_usdt
      FROM capman.withdrawal_fees
      GROUP BY gt_asset, network
    ),
    common_networks AS MATERIALIZED (
      SELECT casf.gt_asset, smf.exchange AS exchange_from, casf.network,
             smt.exchange AS exchange_to,
             casf.gt_source AS source_from, casto.gt_source AS source_to
      FROM capman.capman_asset_info casf
      INNER JOIN capman.capman_asset_info casto
        ON casf.gt_source != casto.gt_source
       AND casf.gt_asset = casto.gt_asset
       AND casf.network = casto.network
       AND casf.can_withdraw
       AND casto.can_deposit
      INNER JOIN capman.sources_map smf ON smf.gt_source = casf.gt_source
      INNER JOIN capman.sources_map smt ON smt.gt_source = casto.gt_source
    ),
    asset_data AS (
      SELECT cn.*,
             ra IS NOT NULL AS is_active,
             wf.gt_fee_in_usdt AS fee_usdt,
             tv.transfer_count,
             tv.fee_paid_usdt,
             COUNT(cn) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to) AS network_count,
             COUNT(ra) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to) AS active_network_count,
             MIN(COALESCE(wf.gt_fee_in_usdt, avg_fee_usdt)) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to) AS cheepest_network_fee,
             FIRST_VALUE(cn.network) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to ORDER BY COALESCE(wf.gt_fee_in_usdt, avg_fee_usdt)) AS cheepest_network,
             FIRST_VALUE(ra IS NOT NULL) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to ORDER BY COALESCE(wf.gt_fee_in_usdt, avg_fee_usdt)) AS cheepest_active
      FROM common_networks cn
      LEFT JOIN capman.route_activity ra
        ON ra.gt_source_from = cn.source_from
       AND ra.gt_source_to = cn.source_to
       AND ra.network = cn.network
       AND ra.gt_asset = cn.gt_asset
      LEFT JOIN capman.withdrawal_fees wf
        ON wf.gt_source = cn.source_from
       AND wf.gt_asset = cn.gt_asset
       AND wf.network = cn.network
      INNER JOIN avg_fees af
        ON af.gt_asset = cn.gt_asset
       AND af.network = cn.network
      LEFT JOIN transfer_vol tv
        ON cn.gt_asset = tv.gt_asset
       AND cn.network = tv.network
       AND cn.source_from = tv.gt_source_from
       AND cn.source_to = tv.gt_source_to
    )
    SELECT *,
           (fee_paid_usdt - cheepest_network_fee * transfer_count) AS potential_saving_usdt
    FROM asset_data
    WHERE network_count > 1
      AND active_network_count > 0
      AND network <> cheepest_network
      AND fee_paid_usdt IS NOT NULL
      AND (fee_paid_usdt - cheepest_network_fee * transfer_count) >= {min_savings}
      AND is_active = true
      AND LOWER(gt_asset) <> 'btc'
      AND NOT (
        LOWER(exchange_from) LIKE '%binance%' AND LOWER(exchange_to) LIKE '%binance%'
      )
      AND NOT (
        (LOWER(exchange_from) = 'tokocrypto' AND LOWER(exchange_to) = 'binance') OR
        (LOWER(exchange_from) = 'binance' AND LOWER(exchange_to) = 'tokocrypto')
      )
    ORDER BY potential_saving_usdt DESC
    """

    print("[✓] Loading SQL-based multi-network analysis...")
    return pl.read_database_uri(sql_query, uri, engine="connectorx").lazy()