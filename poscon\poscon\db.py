import pandas as pd
import psycopg
import yaml
import os
import glob
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional


def load_config(config_path: str = "config.local.yml") -> Dict[str, Any]:
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
    
    return config


def find_csv_files(csv_path: str) -> List[str]:
    if os.path.isfile(csv_path):
        return [csv_path]
    elif os.path.isdir(csv_path):
        return glob.glob(os.path.join(csv_path, "*.csv"))
    else:
        raise FileNotFoundError(f"CSV path not found: {csv_path}")


def convert_timestamp_to_date(df: pd.DataFrame, timestamp_col: str = "Start Timestamp") -> pd.DataFrame:
    df = df.copy()
    
    df[timestamp_col] = pd.to_datetime(df[timestamp_col], unit='ms')
    
    df['Date'] = df[timestamp_col].dt.date
    
    df = df.drop(columns=[timestamp_col])
    
    return df


def filter_blacklisted_data(df: pd.DataFrame, blacklists: Dict[str, List[str]]) -> pd.DataFrame:
    df_filtered = df.copy()
    
    if 'exchanges' in blacklists and blacklists['exchanges']:
        exchange_blacklist = blacklists['exchanges']
        initial_count = len(df_filtered)
        df_filtered = df_filtered[~df_filtered['Exchange'].isin(exchange_blacklist)]
        removed_exchanges = initial_count - len(df_filtered)
        print(f"Removed {removed_exchanges} rows with blacklisted exchanges: {exchange_blacklist}")
    
    if 'assets' in blacklists and blacklists['assets']:
        asset_blacklist = blacklists['assets']
        initial_count = len(df_filtered)
        df_filtered = df_filtered[~df_filtered['Asset'].isin(asset_blacklist)]
        removed_assets = initial_count - len(df_filtered)
        print(f"Removed {removed_assets} rows with blacklisted assets: {asset_blacklist}")
    
    return df_filtered


def load_and_process_data(config_path: str = "config.yml") -> pd.DataFrame:

    config = load_config(config_path)
    
    input_path = config.get('input_csv_path', 'input/')
    
    csv_files = find_csv_files(input_path)
    print(f"Found {len(csv_files)} CSV file(s): {csv_files}")
    
    dataframes = []
    for csv_file in csv_files:
        print(f"Loading {csv_file}...")
        df = pd.read_csv(csv_file)
        dataframes.append(df)
    
    if len(dataframes) == 1:
        combined_df = dataframes[0]
    else:
        combined_df = pd.concat(dataframes, ignore_index=True)
    
    print(f"Loaded {len(combined_df)} total rows from CSV file(s)")
    
    processed_df = convert_timestamp_to_date(combined_df)
    print("Converted timestamp to date")
    
    blacklists = config.get('blacklists', {})
    if blacklists:
        processed_df = filter_blacklisted_data(processed_df, blacklists)
    
    print(f"Final processed dataframe: {len(processed_df)} rows, {len(processed_df.columns)} columns")
    print(f"Date range: {processed_df['Date'].min()} to {processed_df['Date'].max()}")
    print(f"Unique exchanges: {processed_df['Exchange'].nunique()}")
    print(f"Unique assets: {processed_df['Asset'].nunique()}")
    
    return processed_df


def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:

    return {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'date_range': {
            'start': str(df['Date'].min()),
            'end': str(df['Date'].max())
        },
        'unique_exchanges': df['Exchange'].nunique(),
        'unique_assets': df['Asset'].nunique(),
        'exchanges': sorted(df['Exchange'].unique().tolist()),
        'assets': sorted(df['Asset'].unique().tolist()),
        'columns': df.columns.tolist()
    }


def get_transaction_data_for_asset(exchange: str, asset: str, date_range: List, config: Dict) -> pd.DataFrame:

    print(f"\nQuerying database for {exchange} - {asset}")
    print(f"Analysis dates: {date_range}")
    
    fiat_currencies = config.get('fiat_currencies', [])
    if asset in fiat_currencies:
        print(f"Skipping {asset} - fiat currency (not in database)")
        return pd.DataFrame()
    
    if not date_range:
        print("No dates provided")
        return pd.DataFrame()
    
    first_date = min(date_range)
    last_date = max(date_range)
    
    date_from = datetime.combine(first_date, datetime.min.time()) - timedelta(days=1)
    date_to = datetime.combine(last_date, datetime.max.time().replace(microsecond=0)) + timedelta(days=1)
    
    print(f"Database query range: {date_from} to {date_to}")
    
    sql_query = """
    WITH params AS (
        SELECT
            %s::text AS exchange_name,
            %s::text AS asset_name,
            %s::timestamp AS date_from,
            %s::timestamp AS date_to
    )
    SELECT
        t.gt_id,
        t.gt_timestamp,
        sa.gt_exchange  AS source_from_exchange,
        sb.gt_exchange  AS source_to_exchange,
        t.comment,
        t.gt_asset,
        CASE
            WHEN sb.gt_exchange = p.exchange_name THEN ABS(t.gt_amount)
            WHEN sa.gt_exchange = p.exchange_name THEN -ABS(t.gt_amount)
            ELSE t.gt_amount
        END                              AS adjusted_gt_amount,
        CASE
            WHEN sb.gt_exchange = p.exchange_name THEN ABS(t.gt_amount_in_usdt)
            WHEN sa.gt_exchange = p.exchange_name THEN -ABS(t.gt_amount_in_usdt)
            ELSE t.gt_amount_in_usdt
        END                              AS adjusted_gt_amount_in_usdt,
        t.gt_ts_created,
        t.ts_received,
        t.gt_status_id,
        t.address,
        t.memo,
        t.network,
        t.cycle_id,
        t.external_id,
        t.transaction_id,
        t.linked_transfer_id,
        t.gt_fee,
        t.gt_fee_asset,
        t.gt_fee_in_usdt,
        t.last_updated,
        t.gt_source_author,
        t.gt_source_from,
        t.gt_source_to
    FROM            capman.transfers t
    LEFT JOIN       capman.sources   sb ON t.gt_source_to   = sb.gt_id
    LEFT JOIN       capman.sources   sa ON t.gt_source_from = sa.gt_id
    CROSS JOIN      params           p          
    WHERE       t.gt_asset     = p.asset_name
            AND t.gt_timestamp BETWEEN p.date_from AND p.date_to
            AND (sa.gt_exchange = p.exchange_name OR sb.gt_exchange = p.exchange_name)
    ORDER BY
        t.gt_status_id DESC,
        t.gt_timestamp DESC;
    """
    
    try:
        db_config = config.get('database')
        if not db_config:
            print("No database configuration found")
            return pd.DataFrame()
        
        username = db_config.get('username') or db_config.get('user')
        if not username:
            print("No username/user found in database config")
            return pd.DataFrame()
            
        conn_str = f"postgresql://{username}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        
        with psycopg.connect(conn_str) as conn:
            df = pd.read_sql_query(
                sql_query, 
                conn, 
                params=[exchange, asset, date_from, date_to]
            )
        
        print(f"Retrieved {len(df)} transaction records")
        return df
        
    except Exception as e:
        print(f"Database query failed: {e}")
        return pd.DataFrame()
