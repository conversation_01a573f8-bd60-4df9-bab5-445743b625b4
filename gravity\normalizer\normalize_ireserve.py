from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class IreserveNormalizer(Normalizer):
    exchange = "ireserve"

    def normalize_row(self, row, file_name):
        # row = {"Type": "Deposit", "Amount": 1123123}
        #2 Jan 2024 05:27:33 +11:00
        time_format = "%d %b %Y %H:%M:%S %z"
        _timestamp = int(datetime.strptime(row["Date"], time_format).timestamp())
        try:
            comment = row['Comment'].split(":")
            address = comment[1] if len(comment) > 1 else None
            tag = comment[2] if len(comment) > 2 else None
        except AttributeError:
            comment, address, tag = None, None, None

        debit = row['Debit']
        credit = row['Credit']

        if not debit and not credit:
            print(f"Skipping {self.exchange} row, no credit/debit info; {row}")
            return None
        
        if "Deposit" in row['Type']:
            return Deposits(
                row['Currency'],
                float(credit),
                int(datetime.strptime(row["Date"], time_format).timestamp()),
                self.exchange,
                0,
                None,
                None,
                row['BlockchainTransaction'],
            )
        elif "Withdrawal" in row['Type']:
            return Withdrawals(
                row['Currency'],
                float(debit),
                int(datetime.strptime(row["Date"], time_format).timestamp()),
                self.exchange,
                0,
                address,
                tag,
                row['BlockchainTransaction'],
            )