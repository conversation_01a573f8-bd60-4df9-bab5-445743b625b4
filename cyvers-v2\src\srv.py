import logging
import os
import logging.handlers
import asyncio
import time
from typing import Optional
from aiohttp import web
from prometheus_client import Gauge, generate_latest

from . import ctx
from .cfg import TidFilter, PROM_NS
from .misc import ERROR_COUNTER


class Server:
    def __init__(self, req_log: logging.Logger):
        self.log = logging.getLogger(self.__class__.__name__)
        self.req_log: logging.Logger = req_log
        self.app: web.Application = self.app()
        self.site: Optional[web.TCPSite] = None
        self.__m_async: Gauge = Gauge(
            namespace=PROM_NS,
            subsystem="async",
            name="tasks",
            labelnames=["object"],
            documentation="Asyncio related stats",
        )

    def app(self) -> web.Application:
        pub_path = ctx.ctx.cfg.yml.get("public").get("path")
        app = web.Application()
        app.add_routes(
            [
                # private routes
                web.get("/", self.index),
                web.get("/hp", self.alb_hp),
                web.get("/metrics", self.metrics),
                # public roues
                web.get(f"/{pub_path}/hp", self.alb_hp),
                web.post(f"/{pub_path}/cyvers", self.cyvers_hook),
            ]
        )
        return app

    def access_logger(self) -> logging.Logger:
        logger = self.log.getChild("access_log")
        log_file = str(os.path.abspath(os.path.join(ctx.ctx.cfg.args.log_dir, "access.log")))
        handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_file,
            when="D",
            backupCount=10,
            utc=True,
            delay=False,
        )
        handler.setFormatter(logging.Formatter(fmt="%(asctime)s:%(levelname)s:%(tid)s:%(message)s"))
        handler.addFilter(TidFilter())
        logger.addHandler(handler)
        logger.propagate = False
        return logger

    async def start(self) -> None:
        self.log.info(f"Server startup {ctx.ctx.cfg.args.addr}:{ctx.ctx.cfg.args.port}")
        runner = web.AppRunner(self.app, access_log=self.access_logger(), access_log_format="%a [%r] %s %b %Tf")
        await runner.setup()
        self.site = web.TCPSite(runner=runner, host=ctx.ctx.cfg.args.addr, port=ctx.ctx.cfg.args.port)
        await self.site.start()
        ctx.ctx.run = True

    def sighandler(self) -> None:
        # await self.site.stop()
        ctx.ctx.run = False

    async def stop(self) -> None:
        await self.site.stop()

    async def keepalive(self) -> None:
        self.log.info("Keepalive loop engaged")
        while ctx.ctx.run:
            await asyncio.sleep(1)

    async def index(self, request: web.Request):
        ctx.ctx.tid_bump()
        return web.Response(text="index")

    async def alb_hp(self, request: web.Request) -> web.Response:
        ctx.ctx.tid_bump()
        return web.Response(status=200, text=f"{time.time()}")

    async def cyvers_hook(self, request: web.Request) -> web.Response:
        ctx.ctx.tid_bump()
        try:
            payload = await request.json()
            self.req_log.info(
                f"CyversHook payload={payload}",
            )
            self.req_log.info(f"CyversHook headers={request.headers}")
            await ctx.ctx.proxy.webhook_handler(payload, request.headers)
            return web.Response(status=200, text="received")
        except Exception as e:
            self.log.error(f"Webhook handling error", exc_info=e)
            ERROR_COUNTER.labels(scope=self.__class__.__name__.lower()).inc()
            return web.Response(status=500, text=f"Malfunction {ctx.ctx.tid_value}")

    async def metrics(self, request: web.Request):
        ctx.ctx.tid_bump()
        self.__m_async.labels(object="tasks").set(len(asyncio.all_tasks()))
        self.__m_async.labels(object="task_dump").set(len(ctx.ctx.task_dump))
        return web.Response(body=generate_latest())
