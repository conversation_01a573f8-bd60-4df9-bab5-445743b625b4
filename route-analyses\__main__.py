from __future__ import annotations

import argparse
from pathlib import Path
import polars as pl
import pandas as pd


from .db import load_cfg, pg_uri, load_targeted_data, find_route_savings_opportunities
from .analyses import build_single_network_analysis



def save_to_excel(single_network_df: pl.DataFrame, multi_network_df: pl.DataFrame, output_path: Path) -> None:
    """Save both dataframes to Excel with separate sheets."""

    single_network_pandas = single_network_df.to_pandas()
    multi_network_pandas = multi_network_df.to_pandas()

    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        single_network_pandas.to_excel(writer, sheet_name='Single Network', index=False)
        multi_network_pandas.to_excel(writer, sheet_name='Multi Network', index=False)

    print(f"[✓] Wrote Excel file with {len(single_network_df)} single network and {len(multi_network_df)} multi-network rows to {output_path}")


def main() -> None:
    """Main entry point for route analysis."""

    ap = argparse.ArgumentParser(description="Route optimization analysis")
    
    default_config = "config.local.yml"
    if not Path(default_config).exists():
        default_config = "route-analyses/config.local.yml"
    
    ap.add_argument("--config", default=default_config, help="Configuration file path")
    ap.add_argument("--out", default="route_analysis.xlsx", help="Output Excel file")
    ap.add_argument("--days", type=int, default=30, help="Days to look back (default: 30)")
    ap.add_argument("--min-savings-single", type=float, default=500.0,
                    help="Minimum savings threshold for single network cases (default: 500)")
    ap.add_argument("--min-savings-multi", type=float, default=100.0,
                    help="Minimum savings threshold for multi-network cases (default: 100)")
    args = ap.parse_args()

    cfg = load_cfg(args.config)
    uri = pg_uri(cfg)

    print(f"[✓] Using {args.days}-day lookback window")
    print(f"[✓] Single network minimum savings: ${args.min_savings_single}")
    print(f"[✓] Multi-network minimum savings: ${args.min_savings_multi}")

    print("[✓] Building single network analysis...")
    tables = load_targeted_data(uri, days=args.days)
    single_network_result = build_single_network_analysis(tables, min_savings=args.min_savings_single).collect()

    print("[✓] Building multi-network analysis...")
    multi_network_result = find_route_savings_opportunities(uri, days=args.days, min_savings=args.min_savings_multi).collect()

    output_path = Path(args.out)
    save_to_excel(single_network_result, multi_network_result, output_path)

    print("\nSingle Network Analysis Preview:")
    print(single_network_result.head())
    print(f"\nMulti-Network Analysis Preview:")
    print(multi_network_result.head())

if __name__ == "__main__":
    main()