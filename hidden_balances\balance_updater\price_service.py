"""
Price service for cryptocurrency price conversion.
"""

import requests
from decimal import Decima<PERSON>
from typing import Dict

from .binance_client import BinanceClient


class PriceService:
    """Service for handling cryptocurrency price conversions."""
    
    def __init__(self, binance_client: BinanceClient):
        """
        Initialize price service.
        
        Args:
            binance_client: BinanceClient instance for price data
        """
        self.binance_client = binance_client
        self._price_cache: Dict[str, Decimal] = {}
    
    def get_price(self, symbol: str) -> Decimal:
        """
        Get latest price for a trading symbol with caching.
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            
        Returns:
            Price as Decimal
        """
        if symbol not in self._price_cache:
            self._price_cache[symbol] = self.binance_client.get_price(symbol)
        return self._price_cache[symbol]
    
    def price_to_usdt(self, asset: str) -> Decimal:
        """
        Convert asset to USDT equivalent price.
        
        Args:
            asset: Asset symbol (e.g., 'BTC', 'ETH')
            
        Returns:
            USDT price per unit of asset
        """
        if asset in ("USDT", "USD", "BUSD"):  # Common stablecoins
            return Decimal(1)
        
        if asset == "BTC":
            return self.get_price("BTCUSDT")
        
        # Skip assets that already contain USDT to avoid LDUSDTUSDT-like pairs
        if "USDT" in asset:
            return Decimal(1)  # Treat as stablecoin
        
        # Try direct USDT pair first
        symbol = f"{asset}USDT"
        try:
            return self.get_price(symbol)
        except requests.HTTPError:
            # Fallback via BTC if it exists
            try:
                asset_btc = self.get_price(f"{asset}BTC")
                return asset_btc * self.get_price("BTCUSDT")
            except requests.HTTPError:
                # If no pairs exist, treat as worthless to avoid crashes
                print(f"Warning: No price found for {asset}, treating as 0")
                return Decimal(0)
    
    def price_to_btc(self, asset: str) -> Decimal:
        """
        Convert asset to BTC equivalent price.
        
        Args:
            asset: Asset symbol (e.g., 'ETH', 'USDT')
            
        Returns:
            BTC price per unit of asset
        """
        if asset == "BTC":
            return Decimal(1)
        
        if asset in ("USDT", "USD", "BUSD"):
            return Decimal(1) / self.get_price("BTCUSDT")
        
        # Skip assets that already contain BTC to avoid invalid pairs
        if "BTC" in asset:
            return Decimal(0)  # Conservative approach
        
        # Try direct BTC pair first
        symbol = f"{asset}BTC"
        try:
            return self.get_price(symbol)
        except requests.HTTPError:
            # Fallback via USDT if it exists
            try:
                asset_usdt = self.get_price(f"{asset}USDT")
                return asset_usdt / self.get_price("BTCUSDT")
            except requests.HTTPError:
                # If no pairs exist, treat as worthless to avoid crashes
                print(f"Warning: No price found for {asset}, treating as 0")
                return Decimal(0)
    
    def clear_cache(self):
        """Clear the price cache to force fresh price fetches."""
        self._price_cache.clear()
