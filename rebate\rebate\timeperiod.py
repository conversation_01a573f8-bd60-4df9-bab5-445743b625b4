from typing import List
from dataclasses import dataclass

DEFAULT_INTERVAL_S = 60 * 60 * 24
MAX_INTERVAL_S = DEFAULT_INTERVAL_S * 7


@dataclass
class TimePeriod:
    _time_from: float = 0
    _time_to: float = 0

    def __iter__(self):
        return iter((self._time_from, self._time_to))

    def __str__(self):
        return f"[{int(self._time_from)}-{int(self._time_to)}]"

    def __hash__(self):
        return hash((self._time_from, self._time_to))

    @property
    def to(self) -> float:
        return self._time_to

    @property
    def since(self) -> float:
        return self._time_from

    @property
    def duration(self) -> float:
        return self._time_to - self._time_from

    def is_within(self, t: float) -> bool:
        return self.since <= t < self.to

    def reset(self):
        self.update()

    def update(self, time_from: float = 0, time_to: float = 0):
        self._time_from = time_from
        self._time_to = time_to

    def split(self, split: int = 24 * 60 * 60) -> List["TimePeriod"]:
        split = max(split, MAX_INTERVAL_S)
        periods = []
        for i in range(int(self.duration // split + 1)):
            period_end = self._time_to - split * i
            period_start = max(self._time_to - split * (i + 1), self._time_from)
            if period_end > period_start:
                periods.append(TimePeriod(period_start, period_end))
        return periods


if __name__ == "__main__":
    period = TimePeriod(1641471077, 1642075877 + 10)
    for time_from, time_to in period.split():
        print(f"from_db {time_from}-{time_to}")
    # print(tt.to)
    # tfrom, tto = tt
    # print(tfrom)
    # tt = TimePeriod()
