from datetime import datetime

from gravity.normalizer.normal import Normalizer, Deposits, Withdra<PERSON>s

def transform_luno_coin(coin):
    mapping = {
        "XBT": "BTC",

    }
    if coin in mapping:
        return mapping[coin]
    return coin

class LumoNormalizer(Normalizer):
    exchange = "luno"
    encoding = "Latin-1"

    def normalize_row(self, row, file_name):
        time_format = "%Y-%m-%d %H:%M:%S"
        if "deposit" in row["Description"] or "Received " in row["Description"]:
            return Deposits(
                transform_luno_coin(row["Currency"]),
                abs(float(row["Balance delta"])),
                int(datetime.strptime(row["Timestamp (UTC)"], time_format).timestamp()),
                self.exchange,
                0,
                row["Cryptocurrency address"],
                None,
                row["Cryptocurrency transaction ID"],
            )
        elif "Sent " in row["Description"] or "withdrawal" in row["Description"]:
            return Withdrawals(
                transform_luno_coin(row["Currency"]),
                abs(float(row["Balance delta"])),
                int(datetime.strptime(row["Timestamp (UTC)"], time_format).timestamp())-(60*60*2),
                self.exchange,
                0,
                row["Cryptocurrency address"],
                None,
                row["Cryptocurrency transaction ID"],

            )


