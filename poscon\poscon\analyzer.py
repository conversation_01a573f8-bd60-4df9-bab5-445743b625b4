"""Analyzer module for processing position data and creating summaries."""

import pandas as pd
import yaml
from datetime import datetime, timedelta
from typing import Dict, Any, List

try:
    from .db import load_and_process_data, load_config, get_transaction_data_for_asset
    from .output_generator import PosconExcelGenerator
except ImportError:
    # Handle case when run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from poscon.db import load_and_process_data, load_config, get_transaction_data_for_asset
    from poscon.output_generator import PosconExcelGenerator


def analyze_unexplained_by_exchange_and_date(df: pd.DataFrame) -> pd.DataFrame:
    """
    Requires at least 3 dates and excludes the first and last dates from analysis.
        
    Returns:
        DataFrame with Exchange as rows and dates as columns,
        containing sum of 'Unexplained modified $' for each combination
    """
    required_columns = ['Exchange', 'Date', 'Unexplained modified $']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    print(f"Analyzing {len(df)} rows of data...")
    print(f"Unique exchanges: {df['Exchange'].nunique()}")
    print(f"Unique dates: {df['Date'].nunique()}")
    print(f"Date range: {df['Date'].min()} to {df['Date'].max()}")
    
    unique_dates = sorted(df['Date'].unique())
    if len(unique_dates) < 3:
        raise ValueError(f"Analysis requires at least 3 dates, but only {len(unique_dates)} dates found: {unique_dates}")
    
    first_date = unique_dates[0]
    last_date = unique_dates[-1]
    middle_dates = unique_dates[1:-1]
    
    print(f"Excluding first date: {first_date}")
    print(f"Excluding last date: {last_date}")
    print(f"Analyzing {len(middle_dates)} middle dates: {middle_dates}")
    
    filtered_df = df[df['Date'].isin(middle_dates)].copy()
    print(f"Filtered to {len(filtered_df)} rows after excluding first and last dates")
    
    grouped = filtered_df.groupby(['Exchange', 'Date'])['Unexplained modified $'].sum().reset_index()
    
    print(f"Grouped data has {len(grouped)} rows")
    
    pivot_df = grouped.pivot(index='Exchange', columns='Date', values='Unexplained modified $')
    pivot_df = pivot_df.fillna(0)
    pivot_df = pivot_df.reset_index()
    
    date_columns = [col for col in pivot_df.columns if col != 'Exchange']
    date_columns_sorted = sorted(date_columns)
    pivot_df = pivot_df[['Exchange'] + date_columns_sorted]
    
    for col in date_columns_sorted:
        pivot_df[col] = pivot_df[col].round(2)
    
    print(f"Final analysis: {len(pivot_df)} exchanges, {len(date_columns_sorted)} date columns")
    
    return pivot_df


def get_analysis_summary(analysis_df: pd.DataFrame) -> Dict[str, Any]:
    date_columns = [col for col in analysis_df.columns if col != 'Exchange']
    
    summary = {
        'total_exchanges': len(analysis_df),
        'date_columns': len(date_columns),
        'dates': [str(date) for date in date_columns],
        'total_unexplained_per_date': {},
        'exchanges_with_unexplained': {},
        'top_exchanges_by_total_unexplained': []
    }
    
    for date_col in date_columns:
        total = analysis_df[date_col].sum()
        non_zero_count = (analysis_df[date_col] != 0).sum()
        summary['total_unexplained_per_date'][str(date_col)] = {
            'total': float(total),
            'exchanges_with_data': int(non_zero_count)
        }
    
    if date_columns:
        analysis_df['Total_Unexplained'] = analysis_df[date_columns].sum(axis=1)
        
        top_exchanges = analysis_df.nlargest(10, 'Total_Unexplained')[['Exchange', 'Total_Unexplained']]
        summary['top_exchanges_by_total_unexplained'] = [
            {'exchange': row['Exchange'], 'total_unexplained': float(row['Total_Unexplained'])}
            for _, row in top_exchanges.iterrows()
        ]
    
    return summary


def run_analysis(config_path: str = "config.local.yml") -> tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Run the complete analysis pipeline.
        
    Returns:
        Tuple of (analysis_dataframe, summary_dict)
    """
    print("Starting analysis pipeline...")
    print("=" * 50)
    
    df = load_and_process_data(config_path)
    
    print("\n" + "=" * 50)
    print("Running unexplained analysis...")
    print("=" * 50)
    
    analysis_df = analyze_unexplained_by_exchange_and_date(df)
    
    summary = get_analysis_summary(analysis_df)
    
    print("\n" + "=" * 50)
    print("Analysis completed!")
    print("=" * 50)
    
    return analysis_df, summary

def identify_problematic_exchanges(analysis_df: pd.DataFrame, threshold: float) -> List[str]:
    """
    Identify exchanges with unexplained amounts exceeding the threshold.
        
    Returns:
        List of exchange names that exceed the threshold
    """
    date_columns = [col for col in analysis_df.columns if col != 'Exchange']
    
    problematic_exchanges = []
    
    for _, row in analysis_df.iterrows():
        exchange = row['Exchange']
        
        for date_col in date_columns:
            unexplained_amount = abs(row[date_col])
            if unexplained_amount > threshold:
                problematic_exchanges.append(exchange)
                print(f"Flagged exchange: {exchange} (${row[date_col]:,.2f} on {date_col})")
                break
    
    return problematic_exchanges


def analyze_problematic_assets(original_df: pd.DataFrame, exchange: str, threshold: float) -> pd.DataFrame:
    """
    Analyze individual assets for a problematic exchange.
        
    Returns:
        DataFrame with asset-level analysis for the exchange
    """
    print(f"\nAnalyzing assets for exchange: {exchange}")
    print("-" * 50)
    
    exchange_data = original_df[original_df['Exchange'] == exchange].copy()
    
    if len(exchange_data) == 0:
        print(f"No data found for exchange: {exchange}")
        return pd.DataFrame()
    
    unique_dates = sorted(exchange_data['Date'].unique())
    if len(unique_dates) < 3:
        print(f"Exchange {exchange} has insufficient dates for analysis: {unique_dates}")
        return pd.DataFrame()
    
    middle_dates = unique_dates[1:-1]
    filtered_data = exchange_data[exchange_data['Date'].isin(middle_dates)].copy()
    
    print(f"Analyzing {len(filtered_data)} rows for {len(middle_dates)} middle dates: {middle_dates}")
    
    asset_analysis = filtered_data.groupby(['Asset', 'Date'])['Unexplained modified $'].sum().reset_index()
    
    asset_pivot = asset_analysis.pivot(index='Asset', columns='Date', values='Unexplained modified $')
    asset_pivot = asset_pivot.fillna(0).reset_index()
    
    date_columns = [col for col in asset_pivot.columns if col != 'Asset']
    for col in date_columns:
        asset_pivot[col] = asset_pivot[col].round(2)
    
    if date_columns:
        asset_pivot['Total_Unexplained'] = asset_pivot[date_columns].sum(axis=1)
    
    asset_pivot['Flagged'] = abs(asset_pivot['Total_Unexplained']) > threshold
    
    asset_pivot = asset_pivot.reindex(asset_pivot['Total_Unexplained'].abs().sort_values(ascending=False).index)
    
    flagged_assets = asset_pivot[asset_pivot['Flagged']]
    print(f"Found {len(flagged_assets)} assets exceeding ${threshold:,.0f} threshold:")
    
    for _, row in flagged_assets.iterrows():
        asset = row['Asset']
        total = row['Total_Unexplained']
        print(f"  - {asset}: ${total:,.2f}")
    
    return asset_pivot



def identify_systematic_explanations(transaction_data: pd.DataFrame, analysis_dates: List, exchange: str, asset: str) -> Dict[str, Any]:
    """        
    Returns:
        Dictionary with potential explanations and adjustments
    """
    print(f"\nAnalyzing systematic explanations for {exchange} - {asset}")
    print("-" * 60)
    
    if transaction_data.empty:
        print("No transaction data to analyze")
        return {'adjustments': [], 'total_adjustment_usd': 0.0}
    
    adjustments = []
    total_adjustment_usd = 0.0
    
    # Convert analysis dates to datetime for comparison
    analysis_date_strings = [date.strftime('%Y-%m-%d') for date in analysis_dates]
    print(f"Analysis dates: {analysis_date_strings}")
    
    # Case 1a: Status 200 transfers - handle ts_created vs ts_received timing
    print(f"\n1a. Checking Status 200 transfers with ts_created/ts_received logic...")
    
    status_200_transfers = transaction_data[transaction_data['gt_status_id'] == 200].copy()
    
    case1a_adjustment = 0.0
    case1a_count = 0
    
    if not status_200_transfers.empty:
        status_200_transfers['created_date'] = status_200_transfers['gt_ts_created'].dt.date
        status_200_transfers['received_date'] = status_200_transfers['ts_received'].dt.date
        
        for _, transfer in status_200_transfers.iterrows():
            created_date = transfer['created_date']
            received_date = transfer['received_date']
            amount_usd = transfer['adjusted_gt_amount_in_usdt']
            transfer_id = transfer['gt_id']
            
            adjustment_made = False
            reason = ""
            
            if created_date in analysis_dates:
                next_day = created_date + timedelta(days=1)
                if received_date == next_day:
                    case1a_adjustment -= amount_usd
                    adjustment_made = True
                    reason = f"Created {created_date}, received {received_date} (ADD back)"
            
            if received_date in analysis_dates:
                prev_day = received_date - timedelta(days=1)
                if created_date == prev_day:
                    case1a_adjustment += amount_usd
                    adjustment_made = True
                    reason = f"Created {created_date}, received {received_date} (SUBTRACT)"
            
            if adjustment_made:
                case1a_count += 1
                print(f"     Transfer {transfer_id}: {reason} (${amount_usd:,.2f})")
        
        if case1a_adjustment != 0:
            total_adjustment_usd += case1a_adjustment
            adjustments.append({
                'case': 'Status 200 transfers with ts_created/ts_received timing',
                'count': case1a_count,
                'adjustment_usd': case1a_adjustment,
                'reason': 'Status 200 transfers crossing date boundaries: created on target+received next day (ADD), created prev+received on target (SUBTRACT)'
            })
            print(f"   Found {case1a_count} relevant status 200 transfers: ${case1a_adjustment:,.2f}")
        else:
            print("   No status 200 transfers found crossing relevant date boundaries")
    else:
        print("   No status 200 transfers found")
    
    # Case 1b: Status 206 transfers (assumed received, no ts_received) on the date of interest
    print(f"\n1b. Checking Status 206 transfers on analysis dates...")
    status_206_on_date = transaction_data[
        (transaction_data['gt_status_id'] == 206) &
        (transaction_data['gt_timestamp'].dt.date.isin(analysis_dates))
    ]
    
    case1b_adjustment = 0.0
    if not status_206_on_date.empty:
        case1b_adjustment = status_206_on_date['adjusted_gt_amount_in_usdt'].sum()
        total_adjustment_usd += case1b_adjustment
        
        adjustments.append({
            'case': 'Status 206 transfers on analysis date',
            'count': len(status_206_on_date),
            'adjustment_usd': case1b_adjustment,
            'reason': 'Status 206 transfers (assumed received) on analysis date should be subtracted from unexplained'
        })
        print(f"   Found {len(status_206_on_date)} status 206 transfers: ${case1b_adjustment:,.2f}")
        print(f"   Note: Status 206 transfers are assumed to be received (no ts_received timestamp)")
        
        # Show individual transfers for debugging
        for _, transfer in status_206_on_date.iterrows():
            print(f"     Transfer {transfer['gt_id']}: Status 206, "
                  f"{transfer['gt_timestamp'].date()}, ${transfer['adjusted_gt_amount_in_usdt']:,.2f}")
    else:
        print("   No status 206 transfers found on analysis dates")
    
    # Case 2: Status 100-109 transfers that started on the day of interest
    print(f"\n2. Checking Status 100-109 transfers started on analysis dates...")
    status_100_109_started = transaction_data[
        (transaction_data['gt_status_id'].between(100, 109)) &
        (transaction_data['gt_ts_created'].dt.date.isin(analysis_dates))
    ]
    
    if not status_100_109_started.empty:
        case2_adjustment = status_100_109_started['adjusted_gt_amount_in_usdt'].sum()
        total_adjustment_usd -= case2_adjustment
        adjustments.append({
            'case': 'Status 100-109 started on analysis date',
            'count': len(status_100_109_started),
            'adjustment_usd': case2_adjustment,
            'reason': 'Pending/in-progress transfers started on analysis date should be subtracted'
        })
        print(f"   Found {len(status_100_109_started)} status 100-109 transfers: ${case2_adjustment:,.2f}")
    else:
        print("   No status 100-109 transfers found starting on analysis dates")
    
    print(f"\n3. Checking transfers spanning multiple days...")
    
    transaction_data['created_date'] = transaction_data['gt_ts_created'].dt.date
    transaction_data['timestamp_date'] = transaction_data['gt_timestamp'].dt.date
    
    spanning_transfers = transaction_data[
        transaction_data['created_date'] != transaction_data['timestamp_date']
    ]
    
    if not spanning_transfers.empty:
        print(f"   Found {len(spanning_transfers)} transfers spanning multiple days")
        
        case3_adjustment = 0.0
        for _, transfer in spanning_transfers.iterrows():
            created_date = transfer['created_date']
            completed_date = transfer['timestamp_date']
            amount_usd = transfer['adjusted_gt_amount_in_usdt']
            status = transfer['gt_status_id']
            
            created_in_analysis = created_date in analysis_dates
            completed_in_analysis = completed_date in analysis_dates
            
            adjustment_reason = ""
            if created_in_analysis and not completed_in_analysis:
                if status >= 200:
                    case3_adjustment -= amount_usd
                    adjustment_reason = f"Started {created_date}, completed {completed_date} (add back)"
                else:
                    case3_adjustment += amount_usd
                    adjustment_reason = f"Started {created_date}, still pending (subtract)"
            
            elif not created_in_analysis and completed_in_analysis:
                if status >= 200:
                    case3_adjustment += amount_usd
                    adjustment_reason = f"Started {created_date}, completed {completed_date} (subtract)"
            
            if adjustment_reason:
                print(f"     Transfer {transfer['gt_id']}: {adjustment_reason} (${amount_usd:,.2f})")
        
        if case3_adjustment != 0:
            total_adjustment_usd += case3_adjustment
            adjustments.append({
                'case': 'Transfers spanning multiple days',
                'count': len(spanning_transfers),
                'adjustment_usd': case3_adjustment,
                'reason': 'Transfers starting/ending outside analysis period need directional adjustment'
            })
            print(f"   Total case 3 adjustment: ${case3_adjustment:,.2f}")
    else:
        print("   No transfers spanning multiple days found")
    
    print(f"\n{'='*60}")
    print(f"SYSTEMATIC EXPLANATION SUMMARY")
    print(f"{'='*60}")
    print(f"Total adjustments found: {len(adjustments)}")
    print(f"Total adjustment amount: ${total_adjustment_usd:,.2f}")
    
    if adjustments:
        print(f"\nBreakdown:")
        for adj in adjustments:
            print(f"  - {adj['case']}: {adj['count']} transfers, ${adj['adjustment_usd']:,.2f}")
            print(f"    Reason: {adj['reason']}")
    
    return {
        'adjustments': adjustments,
        'total_adjustment_usd': total_adjustment_usd,
        'exchange': exchange,
        'asset': asset,
        'analysis_dates': analysis_date_strings,
        'total_transactions_analyzed': len(transaction_data)
    }


def run_extended_analysis(config_path: str = "config.local.yml", threshold: float = None) -> Dict[str, Any]:

    print("Starting extended analysis pipeline...")
    print("=" * 60)
    
    config = load_config(config_path)
    
    if threshold is None:
        threshold = config.get('processing', {}).get('threshold', 10000.0)
    
    print(f"Threshold: ${threshold:,.0f}")
    
    df = load_and_process_data(config_path)
    
    print("\n" + "=" * 60)
    print("Running main exchange analysis...")
    print("=" * 60)
    analysis_df = analyze_unexplained_by_exchange_and_date(df)
    
    print("\n" + "=" * 60)
    print("Identifying problematic exchanges...")
    print("=" * 60)
    problematic_exchanges = identify_problematic_exchanges(analysis_df, threshold)
    
    if not problematic_exchanges:
        print(f"No exchanges found with unexplained amounts > ${threshold:,.0f}")
        return {'problematic_exchanges': [], 'analysis_complete': True}
    
    results = {
        'threshold': threshold,
        'problematic_exchanges': problematic_exchanges,
        'exchange_details': {},
        'flagged_assets': {},
        'explanations': {}
    }
    
    for exchange in problematic_exchanges:
        print(f"\n{'='*60}")
        print(f"Deep dive into exchange: {exchange}")
        print(f"{'='*60}")
        
        asset_analysis = analyze_problematic_assets(df, exchange, threshold)
        results['exchange_details'][exchange] = asset_analysis
        
        if not asset_analysis.empty:
            flagged_assets = asset_analysis[asset_analysis['Flagged']]['Asset'].tolist()
            results['flagged_assets'][exchange] = flagged_assets
            
            unique_dates = sorted(df['Date'].unique())
            middle_dates = unique_dates[1:-1] if len(unique_dates) >= 3 else unique_dates
            
            for asset in flagged_assets:
                print(f"\n--- Analyzing {asset} ---")
                
                transaction_data = get_transaction_data_for_asset(exchange, asset, middle_dates, config)
                
                explanations = identify_systematic_explanations(transaction_data, middle_dates, exchange, asset)
                
                if exchange not in results.get('explanations', {}):
                    results.setdefault('explanations', {})[exchange] = {}
                results['explanations'][exchange][asset] = explanations
    
    print(f"\n{'='*60}")
    print("Deep analysis completed!")
    print(f"{'='*60}")
    print(f"Problematic exchanges found: {len(problematic_exchanges)}")
    
    return results


def generate_excel_report(analysis_results: Dict[str, Any], config: Dict) -> str:
    
    output_path = config.get('output_excel_path', 'output/poscon_report.xlsx')
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if output_path.endswith('.xlsx'):
        output_path = output_path.replace('.xlsx', f'_{timestamp}.xlsx')
    else:
        output_path = f"{output_path}_{timestamp}.xlsx"
    
    print(f"\n{'='*60}")
    print("GENERATING EXCEL REPORT")
    print(f"{'='*60}")
    
    excel_generator = PosconExcelGenerator(output_path)
    generated_path = excel_generator.generate_poscon_report(analysis_results)
    
    return generated_path


def run_multi_day_analysis(config_path: str = "config.local.yml", threshold: float = None) -> Dict[str, Any]:
    """
    Run analysis by splitting multi-day data into individual 3-day windows.

    Returns:
        Dictionary with combined analysis results from all date windows
    """
    print("Starting Multi-Day POSCON Analysis...")
    print("=" * 70)

    config = load_config(config_path)
    if threshold is None:
        threshold = config.get('processing', {}).get('threshold', 10000.0)

    full_df = load_and_process_data(config_path)
    unique_dates = sorted(full_df['Date'].unique())

    print(f"Total dates in dataset: {len(unique_dates)}")
    print(f"Date range: {unique_dates[0]} to {unique_dates[-1]}")

    if len(unique_dates) <= 3:
        print("Dataset has 3 or fewer dates - using standard single analysis")
        return run_extended_analysis(config_path, threshold)

    print(f"Dataset has {len(unique_dates)} dates - splitting into individual 3-day analyses")

    combined_results = {
        'threshold': threshold,
        'problematic_exchanges': set(),
        'exchange_details': {},
        'flagged_assets': {},
        'explanations': {},
        'analysis_windows': []
    }

    valid_target_dates = unique_dates[1:-1] 

    for target_date in valid_target_dates:
        target_idx = unique_dates.index(target_date)
        window_dates = unique_dates[target_idx-1:target_idx+2]  # 3-day window

        print(f"\n{'='*50}")
        print(f"Analyzing target date: {target_date}")
        print(f"3-day window: {window_dates}")
        print(f"{'='*50}")

        # Filter data to this 3-day window
        window_df = full_df[full_df['Date'].isin(window_dates)].copy()

        if len(window_df) == 0:
            print(f"No data found for window {window_dates} - skipping")
            continue

        # Run analysis on this window
        window_results = run_single_window_analysis(window_df, target_date, threshold, config)

        # Store window info
        combined_results['analysis_windows'].append({
            'target_date': target_date,
            'window_dates': window_dates,
            'results': window_results
        })

        # Merge results into combined results
        if window_results.get('problematic_exchanges'):
            combined_results['problematic_exchanges'].update(window_results['problematic_exchanges'])

        # Store exchange details separately for each date to avoid mixing data
        for exchange, details in window_results.get('exchange_details', {}).items():
            # Create a unique key for each exchange-date combination
            exchange_date_key = f"{exchange}_{target_date}"
            combined_results['exchange_details'][exchange_date_key] = details.copy()

        # Merge flagged assets
        for exchange, assets in window_results.get('flagged_assets', {}).items():
            if exchange not in combined_results['flagged_assets']:
                combined_results['flagged_assets'][exchange] = set(assets)
            else:
                combined_results['flagged_assets'][exchange].update(assets)

        # Merge explanations
        for exchange, exchange_explanations in window_results.get('explanations', {}).items():
            if exchange not in combined_results['explanations']:
                combined_results['explanations'][exchange] = {}
            for asset, asset_explanations in exchange_explanations.items():
                asset_key = f"{asset}_{target_date}"  # Make unique per date
                combined_results['explanations'][exchange][asset_key] = asset_explanations

    # Convert sets back to lists for JSON serialization
    combined_results['problematic_exchanges'] = list(combined_results['problematic_exchanges'])
    for exchange in combined_results['flagged_assets']:
        combined_results['flagged_assets'][exchange] = list(combined_results['flagged_assets'][exchange])

    print(f"\n{'='*70}")
    print("MULTI-DAY ANALYSIS COMPLETED")
    print(f"{'='*70}")
    print(f"Analyzed {len(valid_target_dates)} target dates")
    print(f"Total problematic exchanges found: {len(combined_results['problematic_exchanges'])}")

    return combined_results


def run_single_window_analysis(window_df: pd.DataFrame, target_date, threshold: float, config: Dict) -> Dict[str, Any]:
    """
    Run analysis on a single 3-day window.

    Returns:
        Analysis results for this window
    """
    print(f"Running analysis for target date: {target_date}")

    analysis_df = analyze_unexplained_by_exchange_and_date(window_df)

    problematic_exchanges = identify_problematic_exchanges(analysis_df, threshold)

    if not problematic_exchanges:
        return {'problematic_exchanges': [], 'analysis_complete': True}

    results = {
        'threshold': threshold,
        'problematic_exchanges': problematic_exchanges,
        'exchange_details': {},
        'flagged_assets': {},
        'explanations': {}
    }

    for exchange in problematic_exchanges:
        print(f"\n--- Analyzing exchange: {exchange} for date {target_date} ---")

        asset_analysis = analyze_problematic_assets(window_df, exchange, threshold)
        results['exchange_details'][exchange] = asset_analysis

        if not asset_analysis.empty:
            flagged_assets = asset_analysis[asset_analysis['Flagged']]['Asset'].tolist()
            results['flagged_assets'][exchange] = flagged_assets

            unique_dates = sorted(window_df['Date'].unique())
            middle_dates = unique_dates[1:-1] if len(unique_dates) >= 3 else [target_date]

            for asset in flagged_assets:
                print(f"\n--- Analyzing {asset} for {target_date} ---")

                transaction_data = get_transaction_data_for_asset(exchange, asset, middle_dates, config)

                explanations = identify_systematic_explanations(transaction_data, middle_dates, exchange, asset)

                if exchange not in results.get('explanations', {}):
                    results.setdefault('explanations', {})[exchange] = {}
                results['explanations'][exchange][asset] = explanations

    return results


def run_complete_analysis(config_path: str = "config.local.yml", threshold: float = None, generate_excel: bool = True) -> Dict[str, Any]:
    """
    Run complete POSCON analysis including Excel report generation.

    This function automatically detects if multi-day processing is needed and
    uses the appropriate analysis method.

    Returns:
        Dictionary with analysis results and Excel path
    """
    print("Starting Complete POSCON Analysis...")
    print("=" * 70)

    config = load_config(config_path)

    full_df = load_and_process_data(config_path)
    unique_dates = sorted(full_df['Date'].unique())

    if len(unique_dates) > 3:
        print("Multi-day dataset detected - using split analysis approach")
        analysis_results = run_multi_day_analysis(config_path, threshold)
    else:
        print("Standard 3-day dataset - using single analysis approach")
        analysis_results = run_extended_analysis(config_path, threshold)

    excel_path = None
    if generate_excel:
        excel_path = generate_excel_report(analysis_results, config)

    analysis_results['excel_report_path'] = excel_path

    print(f"\n{'='*70}")
    print("COMPLETE ANALYSIS FINISHED")
    print(f"{'='*70}")

    if excel_path:
        print(f"Excel report generated: {excel_path}")

    return analysis_results

if __name__ == "__main__":
    # Test the complete analyzer with Excel generation
    try:
        # Run complete analysis (includes deep analysis + Excel generation)
        complete_results = run_complete_analysis()
        
        print(f"\n{'='*70}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*70}")
        
        print(f"Threshold used: ${complete_results.get('threshold', 'N/A'):,.0f}")
        print(f"Problematic exchanges found: {len(complete_results.get('problematic_exchanges', []))}")
        
        if complete_results.get('explanations'):
            total_explanations = sum(
                len(assets) for assets in complete_results['explanations'].values()
            )
            print(f"Assets analyzed for systematic explanations: {total_explanations}")
        
        if complete_results.get('excel_report_path'):
            print(f"Excel report generated: {complete_results['excel_report_path']}")
        
        print(f"\nProblematic exchanges:")
        for i, exchange in enumerate(complete_results.get('problematic_exchanges', []), 1):
            print(f"  {i:2d}. {exchange}")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
