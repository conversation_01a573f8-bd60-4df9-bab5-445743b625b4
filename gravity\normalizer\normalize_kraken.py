import datetime 
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

ASSET_MAP = {
    "XXRP": "XRP",
    "XXDG": "DOGE"
}

class KrakenNormalizer(Normalizer):
    exchange = "kraken"

    def normalize_row(self, row, file_name):
        asset = row["asset"]
        asset = ASSET_MAP.get(asset, asset)

        time_format = "%Y-%m-%d %H:%M:%S.%f"
        time_format_short = "%Y-%m-%d %H:%M:%S"

        time_raw = row["time"]
        ts = datetime.datetime.strptime(time_raw, time_format if len(time_raw) > 20 else time_format_short).timestamp()
        amount = float(row["amount"])
        txid = row["blockchain TXID"]
        address = row["address/account"]

        if "DEPOSIT" in row["type"]:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=None,
                txid=txid
            )
        elif "WITHDRAWAL" in row["type"]:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=None,
                txid=txid            
            )