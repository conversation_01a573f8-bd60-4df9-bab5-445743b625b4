default:
  image: registry.gitlab.com/gravity-team/common/zoo/gt-al2-x86_64-cpy3.8-rust:c6i-2024.05.13-07.50
  tags: ["aws_fleet_ec2_c6i"]

variables:
  ARTIFACT_PROJECT_ID: "40121732"

stages:
  - build

build:
  stage: build
  when: always
  script:
    - python -VV
    - echo "==================== Venv prepare ====================" > /dev/null
    - if [ ! -f venv/bin/activate ] ; then python -m venv venv ; else echo "venv exists" ;fi
    - source venv/bin/activate

    - echo "==================== Dependencies ====================" > /dev/null
    - >
      pip install --progress-bar off --disable-pip-version-check --no-color --no-python-version-warning
      --upgrade --upgrade-strategy eager
      -r requirements.ci.txt 2>&1 

    - echo "==================== Rebate ====================" > /dev/null
    - cd ./rebate/
    - if [ -d ./build ] ; then rm -rf ./build ; fi
    - if [ -d ./dist ] ; then rm -rf ./dist ; fi
    - if [ -d ./*.egg-info ] ; then rm -rf ./*.egg-info ; fi
    - python -m pip install -r requirements.txt
    - python -m build --wheel 2>&1
    - ls -lart ./dist/
    - >
      TWINE_PASSWORD="${CI_JOB_TOKEN}" TWINE_USERNAME=gitlab-ci-token
      python -m twine --no-color upload --verbose --non-interactive --disable-progress-bar --skip-existing
      --repository-url ${CI_API_V4_URL}/projects/${ARTIFACT_PROJECT_ID}/packages/pypi
      ./dist/*


#    - echo "==================== Cyverse ====================" > /dev/null
#    - cd ./../cyverse/
#    - if [ -d ./build ] ; then rm -rf ./build ; fi
#    - if [ -d ./dist ] ; then rm -rf ./dist ; fi
#    - if [ -d ./*.egg-info ] ; then rm -rf ./*.egg-info ; fi
#    - python -m pip install -r requirements.txt
#    - python -m build --wheel 2>&1
#    - >
#      TWINE_PASSWORD="${CI_JOB_TOKEN}" TWINE_USERNAME=gitlab-ci-token
#      python -m twine upload
#      --non-interactive --disable-progress-bar
#      --repository-url ${CI_API_V4_URL}/projects/${ARTIFACT_PROJECT_ID}/packages/pypi
#      ./dist/*
