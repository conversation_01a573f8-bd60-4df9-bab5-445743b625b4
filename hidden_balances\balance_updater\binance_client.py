"""
Binance API client for sub-account balance retrieval.
"""

import time
import hmac
import hashlib
from urllib.parse import urlencode
import requests
from typing import Dict, List, Tuple, Union, Optional
from decimal import Decimal

from .config import Config


class BinanceClient:
    """Client for interacting with Binance API."""
    
    def __init__(self, config: Config):
        """
        Initialize Binance client.
        
        Args:
            config: Configuration object with API credentials and settings
        """
        self.config = config
        self.base_url = config.base_url
        self.api_key = config.api_key
        self.api_secret = config.api_secret
        self.timeout = config.timeout
        self.page_size = config.page_size
    
    def _now_ms(self) -> int:
        """Get current timestamp in milliseconds."""
        return int(time.time() * 1000)
    
    def _sign(self, params: dict) -> str:
        """Create signed query string for authenticated requests."""
        query = urlencode(params, doseq=True)
        sig = hmac.new(
            self.api_secret.encode(), 
            query.encode(), 
            hashlib.sha256
        ).hexdigest()
        return f"{query}&signature={sig}"
    
    def _signed_get(self, path: str, params: dict) -> dict:
        """Make authenticated GET request to Binance API."""
        params.setdefault("timestamp", self._now_ms())
        headers = {"X-MBX-APIKEY": self.api_key}
        url = f"{self.base_url}{path}?{self._sign(params)}"
        
        response = requests.get(url, headers=headers, timeout=self.timeout)
        response.raise_for_status()
        return response.json()
    
    def _public_get(self, path: str, params: dict = None) -> Union[dict, list]:
        """Make public GET request to Binance API."""
        url = f"{self.base_url}{path}"
        response = requests.get(url, params=params or {}, timeout=self.timeout)
        response.raise_for_status()
        return response.json()
    
    # Regular sub-account methods
    def get_regular_sub_accounts(self) -> List[str]:
        """Get list of regular sub-account emails."""
        emails = []
        page = 1
        
        while True:
            resp = self._signed_get("/sapi/v1/sub-account/list", {
                "page": page, 
                "limit": 200
            })
            
            rows = resp.get("subAccounts", []) or resp.get("data", []) or resp
            if isinstance(rows, dict) and "subAccounts" in rows:
                rows = rows["subAccounts"]
            
            if not rows:
                break
                
            for item in rows:
                emails.append(item["email"])
            
            if len(rows) < 200:
                break
            page += 1
        
        return emails
    
    def get_regular_spot_btc_totals(self) -> Dict[str, Decimal]:
        """Get spot BTC totals for regular sub-accounts."""
        totals = {}
        page = 1
        
        while True:
            data = self._signed_get("/sapi/v1/sub-account/spotSummary", {
                "page": page, 
                "size": self.page_size
            })
            
            lst = data.get("spotSubUserAssetBtcVoList", [])
            for row in lst:
                totals[row["email"]] = Decimal(row["totalAsset"])
            
            if len(lst) < self.page_size:
                break
            page += 1
        
        return totals
    
    def get_regular_futures_summaries(self) -> Tuple[Dict[str, Decimal], Dict[str, Decimal]]:
        """
        Get futures summaries for regular sub-accounts.
        
        Returns:
            Tuple of (usdtm_totals, coinm_totals)
        """
        def pull_futures(ftype: int) -> Dict[str, Decimal]:
            out = {}
            page = 1
            key = "futureAccountSummaryResp" if ftype == 1 else "deliveryAccountSummaryResp"
            
            while True:
                data = self._signed_get("/sapi/v2/sub-account/futures/accountSummary", {
                    "futuresType": ftype, 
                    "page": page, 
                    "limit": self.page_size
                })
                
                sublist = data.get(key, {}).get("subAccountList", [])
                for row in sublist:
                    balance_key = row.get("totalMarginBalance", row.get("totalWalletBalance", "0"))
                    out[row["email"]] = Decimal(balance_key)
                
                if len(sublist) < self.page_size:
                    break
                page += 1
            
            return out
        
        usdtm = pull_futures(1)  # USDT-M futures
        coinm = pull_futures(2)  # COIN-M futures
        return usdtm, coinm

    def get_regular_margin_btc_totals(self) -> Dict[str, Decimal]:
        """Get margin BTC totals for regular sub-accounts."""
        totals = {}

        try:
            # Call the margin summary endpoint without email parameter to get all accounts
            data = self._signed_get("/sapi/v1/sub-account/margin/accountSummary", {})

            # The response contains a subAccountList with individual account details
            sub_accounts = data.get("subAccountList", [])

            for account in sub_accounts:
                email = account["email"]
                # Use totalAssetOfBtc (includes borrowed funds as available cash)
                total_btc = Decimal(account.get("totalAssetOfBtc", "0"))
                totals[email] = total_btc

        except requests.HTTPError as e:
            # Some accounts may not have margin enabled
            print(f"Warning: Could not fetch margin data - {e}")

        return totals

    # Managed sub-account methods
    def get_managed_sub_accounts(self) -> List[str]:
        """Get list of managed sub-account emails."""
        emails = []
        page = 1

        while True:
            resp = self._signed_get("/sapi/v1/managed-subaccount/info", {
                "page": page,
                "limit": 20
            })

            lst = resp.get("managerSubUserInfoVoList", []) or []
            for r in lst:
                emails.append(r["email"])

            if len(lst) < 20:
                break
            page += 1

        return emails

    def get_managed_spot_btc_total(self, email: str) -> Decimal:
        """Get spot BTC total for a managed sub-account."""
        rows = self._signed_get("/sapi/v1/managed-subaccount/asset", {"email": email})
        total_btc = Decimal("0")

        for r in rows:
            total_btc += Decimal(r["btcValue"])

        return total_btc

    def get_managed_futures_totals(self, email: str, price_service) -> Tuple[Decimal, Decimal]:
        """
        Get futures totals for a managed sub-account.

        Args:
            email: Sub-account email
            price_service: PriceService instance for price conversion

        Returns:
            Tuple of (usdt_total, btc_total)
        """
        usdt_total = Decimal("0")
        btc_total = Decimal("0")

        for acct_type in (None, "USDT_FUTURE", "COIN_FUTURE"):
            params = {"email": email}
            if acct_type:
                params["accountType"] = acct_type

            data = self._signed_get("/sapi/v1/managed-subaccount/fetch-future-asset", params)

            # Structure contains snapshotVos[0]["data"]["assets"]
            for snap in data.get("snapshotVos", []):
                assets = snap.get("data", {}).get("assets", [])
                for a in assets:
                    asset = a["asset"]
                    bal = Decimal(str(a.get("marginBalance") or a.get("walletBalance") or "0"))
                    usdt_total += bal * price_service.price_to_usdt(asset)
                    btc_total += bal * price_service.price_to_btc(asset)

        return usdt_total, btc_total

    def get_managed_margin_totals(self, email: str, price_service) -> Tuple[Decimal, Decimal]:
        """
        Get margin totals for a managed sub-account.

        Args:
            email: Sub-account email
            price_service: PriceService instance for price conversion

        Returns:
            Tuple of (usdt_total, btc_total)
        """
        usdt_total = Decimal("0")
        btc_total = Decimal("0")

        try:
            # Try to get margin asset data for managed sub-account
            data = self._signed_get("/sapi/v1/managed-subaccount/marginAsset", {"email": email})

            for asset_data in data:
                asset = asset_data["asset"]
                # Use netAsset or netAssetOfBtc if available, fallback to free + borrowed
                net_asset = Decimal(str(asset_data.get("netAsset",
                                       asset_data.get("free", "0"))))

                usdt_total += net_asset * price_service.price_to_usdt(asset)
                btc_total += net_asset * price_service.price_to_btc(asset)

        except requests.HTTPError as e:
            # Margin may not be enabled for this managed sub-account
            print(f"Warning: Could not fetch margin data for {email} - {e}")

        return usdt_total, btc_total

    def get_price(self, symbol: str) -> Decimal:
        """Get latest price for a trading symbol."""
        data = self._public_get("/api/v3/ticker/price", {"symbol": symbol})
        return Decimal(data["price"])
