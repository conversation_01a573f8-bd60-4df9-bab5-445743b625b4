from datetime import datetime

from .bitvavo_addresses import ADDRESSES

from typing import List
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class BitvavoNormalizer(Normalizer):
    exchange = "bitvavo"

    def normalize_row(self, row, file_name: str):
        # 2023-12-21 09:13:06
        time_format = "%m/%d/%Y %H:%M"
        ts = datetime.strptime(row["Transaction Timestamp Updated Time"], time_format).timestamp()

        asset_str = row["Transaction Amount Digital Asset"]
        if not asset_str: # we have fiat
            print(f"{self.exchange} skipping {row['Asset']}")
            return None

        amount = float(asset_str.replace(",", ""))

        if row["Wallet Address"] in ADDRESSES:
            return Deposits(
                asset=row["Asset"],
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=row["Wallet Address"],
                tag=None,
                txid=row["Transaction ID"],

            )
        else:
            return Withdrawals(
                asset=row["Asset"],
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=row["Wallet Address"],
                tag=None,
                txid=row["Transaction ID"],
            )