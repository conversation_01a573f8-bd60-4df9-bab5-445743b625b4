from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class OkxNormalizer(Normalizer):
    exchange = "okx"

    def normalize_row(self, row, file_name):
        ts = float(row["requested_ts"])

        asset = row["asset"]
        amount = float(row["amount"])
        txid = str(row.get("txid"))
        address = row["address"]
        tag = row["memo"]

        if "deposit" in file_name:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid,
            )
        elif "withdraw" in file_name:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=address,
                tag=tag,
                txid=txid
        )