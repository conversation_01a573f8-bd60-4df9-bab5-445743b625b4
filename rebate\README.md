# Rebate

Rebate is a scipr/service that calculates rebate for a given time period.
output is written in three files: rebate.csv, top_trade.csv, rebate_contribution.csv
rebate.csv: rebate for each exchange, side and quote
top_trade.csv: top trades for each exchange, side, quote and base
rebate_contribution.csv: rebate contribution for each exchange, quote, base and side,

## Installation
Note: that isn't stored publicly but in gt repo called ZOO
```bash
pip install gt-rebate
```

## Usage
```bash
python -m rebate --config <config_file> --output <output_dir> --start-time <start_time> --end-time <end_time> --sentry-mode --send-slack --bind-port <bind_port> --bind-host <bind_host>
```
- config_file: config file
- output: output dir where rebate and top trades will be written, generated output is in output dir
- start-time: Start time in timestamp
- end-time: End time in timestamp
- sentry-mode: Sentry mode, generate report once a month and sends to slack, generated output is in output dir and slack messages is sent
- bind-port: port used by promethium only used in sentry mode
- bind-host: host used by promethium only used in sentry mode
- send-slack: Send generated report to slack, files are sent to slack as well

If sentry_mode is on, start-time and end-time are not used.

## Config file
config file is a yaml file with the following structure:
```
database:
  user: <user>
  password: <password>
  database: <database>
  host: <host>
  min_size: 2
  port: <port>
slack:
  token: <slack_token>
  channel_id: <slack_channel_id>
```
