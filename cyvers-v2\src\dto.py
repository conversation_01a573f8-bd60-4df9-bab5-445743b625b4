from typing import Dict, Any, Optional, List
from multidict import CIMultiDictProxy
from enum import IntEnum
import time
import datetime


class CyversSeverity(IntEnum):
    UNKNOWN = 0
    INFO = 1
    WARNING = 2
    CRITICAL = 3

    @classmethod
    def from_string(cls, value):
        if value.lower() in ["info"]:
            return cls.INFO
        elif value.lower() in ["warning", "warn", "low", "medium"]:
            return cls.WARNING
        elif value.lower() in ["critical", "crit", "high"]:
            return cls.CRITICAL
        else:
            return cls.UNKNOWN

    def __str__(self) -> str:
        if self.value == 0:
            return "unknown"
        elif self.value == 1:
            return "info"
        elif self.value == 2:
            return "warning"
        elif self.value == 3:
            return "critical"

    def __repr__(self) -> str:
        return str(self)


class CyversAlert:
    def __init__(self, payload: Dict[str, Any], headers: CIMultiDictProxy) -> None:
        self.__payload = payload
        self.id: str = payload.get("id")
        self.alert_name: str = headers.getone("X-Alert-Name", "GtCyversAlert")
        self.last_sent: float = 0.0

        self.startsAt: float = datetime.datetime.strptime(
            payload.get("detection_time"), "%Y-%m-%dT%H:%M:%S%z"
        ).timestamp()
        # self.startsAt: float = time.mktime(time.strptime(payload.get("detection_time"), "%Y-%m-%dT%H:%M:%S%z"))
        self.severity: CyversSeverity = CyversSeverity.from_string(payload.get("severity"))
        self.risk_type: str = payload.get("risk_type")
        self.url: Optional[str] = payload.get("alert_url", None)
        self.risk_category: Optional[str] = payload.get("risk_category", None)
        self.attack_phase: Optional[str] = payload.get("attack_phase", None)
        self.value: float = payload.get("total_value_lost_usd", 0)
        self.entities: List[str] = []
        for entity in payload.get("entity", []):
            if entity.get("name", None) is not None:
                self.entities.append(entity["name"])
        if len(self.entities) == 0:
            self.entities.append("unknown")

    @property
    def json(self) -> Dict:
        return {
            "id": self.id,
            "alert_name": self.alert_name,
            "last_sent": self.last_sent,
            "startsAt": self.startsAt,
            "severity": str(self.severity),
            "risk_type": self.risk_type,
            "url": self.url,
            "risk_category": self.risk_category,
            "attack_phase": self.attack_phase,
            "entities": self.entities,
        }

    def to_prometheus_up(self, realm: str) -> List[Dict[str, Any]]:
        payload: List[Dict[str:Any]] = []
        for entity in self.entities:
            summary = f"{self.risk_category.capitalize()} incident, type {self.risk_type.lower()}"
            description: str = (
                f"Cyvers incident, exchange {entity}, category {self.risk_category.lower()}, type {self.risk_type.lower()}"
            )
            if self.value > 0:
                description += f", value {round(self.value)} USD."
            else:
                description += "."
            data = {
                "startsAt": time.strftime("%Y-%m-%dT%H:%M:%S.000Z", time.gmtime(self.startsAt)),
                "labels": {
                    "alertname": self.alert_name,
                    "severity": str(self.severity).lower(),
                    "risk_type": self.risk_type.lower(),
                    "risk_category": self.risk_category.lower(),
                    "attack_phase": self.attack_phase.lower(),
                    "exchange": entity.lower(),
                    "realm": realm.lower(),
                    "cyvers_id": self.id,
                },
                "annotations": {
                    "summary": summary,
                    "description": description,
                },
            }
            if self.url is not None:
                data["generatorURL"] = self.url
            else:
                data["generatorURL"] = "http://localhost:1235"
            payload.append(data)
        return payload
