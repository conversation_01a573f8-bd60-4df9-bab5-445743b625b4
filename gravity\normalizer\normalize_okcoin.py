from datetime import datetime
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals

class OkcoinNormalizer(Normalizer):
    exchange = "okcoinjp"

    def normalize_row(self, row, file_name):
        # 2022-10-20T17:04:22.893721
        time_format = "%Y-%m-%d %H:%M:%S"
        ts = datetime.strptime(row["date"], time_format).timestamp()

        asset = row["coin"]
        amount = float(row["amount"])
        txid = str(row.get("txid"))
        addr_and_tag = self.extract_addr_tags(row["to_address"])


        if not txid:
            return None

        if "deposit" in file_name:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=addr_and_tag[0],
                tag=addr_and_tag[1] if len(addr_and_tag) > 1 else None,
                txid=txid,
            )
        elif "withdraw" in file_name:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=ts,
                exchange=self.exchange,
                fee=None,
                address=addr_and_tag[0],
                tag=addr_and_tag[1] if len(addr_and_tag) > 1 else None,
                txid=txid,
            )