import csv
import os
from dataclasses import dataclass

from gravity.normalizer.normal import Transfer
from tqdm import tqdm

directory = "../export"
known_addresses = {}
@dataclass
class Trx:
    exchange: str
    ts: float
    asset: str
    amount: float
    txid: str
    gt_id: str

trxs = []

def math_trx_to_transfer(trxs , withdrawal):
    transfers = []
    for trx in trxs:
        if trx.exchange != withdrawal.exchange:
            continue
        if trx.txid == withdrawal.txid:
            return trx
        if trx.asset == withdrawal.asset:
            if (trx.amount > (withdrawal.amount*0.95) and trx.amount < (withdrawal.amount *1.05) and trx.ts > (withdrawal.ts * 0.99995)
                    and trx.ts < (withdrawal.ts * 1.00005)):
                transfers.append(trx)
    return transfers

for file_name in os.listdir(directory):
    with open(os.path.join(directory, file_name)) as csv_deposit_file:
        csv_deposit_reader = csv.DictReader(csv_deposit_file)
        for row in csv_deposit_reader:
            trx = Trx(
                exchange=row["from"],
                ts=float(row["timestamp_requested"]),
                asset=row["coin"],
                amount=float(row["withdrawal_amount"]),
                txid=row["txid"],
                gt_id=row["gt_id"]
            )
            trxs.append(trx)
# load prices
prices = {}
with open("../price.csv", "r") as csv_file:
    csv_reader = csv.DictReader(csv_file)
    for row in csv_reader:
        if row["fee_exchange_rate"] is None:
            continue
        prices[row["coin"]] = float(row["fee_exchange_rate"]) if row["fee_exchange_rate"] else 0
with open("../matched/matched.csv", "w") as csv_file:
    csv_writer = csv.DictWriter(csv_file, fieldnames=Transfer.__dataclass_fields__.keys())
    csv_writer.writeheader()
    for _file in os.listdir("../out"):
        print(_file)
        with open(os.path.join("../out", _file), "r") as csv_deposit_file:
            csv_deposit_reader = csv.DictReader(csv_deposit_file)
            for row in tqdm(csv_deposit_reader):
                transfer = Transfer.from_row(row)
                match = math_trx_to_transfer(trxs, transfer)
                if match:
                    if isinstance(match, list):
                        transfer.potential_match = [x.gt_id for x in match]
                        transfer.does_txid_match_db = False
                    else:
                        transfer.potential_match = match.gt_id
                        transfer.does_txid_match_db = True
                    transfer.value = transfer.amount * prices.get(transfer.asset, 0)
                    csv_writer.writerow(transfer.to_dict())

