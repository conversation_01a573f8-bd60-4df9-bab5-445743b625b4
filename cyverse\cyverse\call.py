from twilio.rest import Client


def format_message(name, category_name, entity_name):
    return f"<Response><Say>WARNING WARNING WARNING. Cyverse Alert. {name} in {category_name} in {entity_name}</Say></Response>"


class Caller:
    def __init__(self, account_sid, auth_token, phone_number, callback_url):
        self.phone_number = phone_number
        self.callback_url = callback_url
        self.client = Client(account_sid, auth_token)

    def make_a_call(self, to, message):
        message = self.client.calls.create(
            to=to,
            from_=self.phone_number,
            twiml=message,
            status_callback=self.callback_url,
            status_callback_method="POST",
        )
        return message
