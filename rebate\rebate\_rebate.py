import asyncio
import csv
import heapq
import os
from typing import Tuple

import asyncpg
from .metrics import Metrics
from .trading_fees_v2 import TradingFeesV2
from alive_progress import alive_bar
from collections import defaultdict

def format_trade_query(start_ts: float, end_ts: float, offset: int, limit: int = 5000):
    return (f"SELECT id, gt_timestamp, exchange, side, order_type, amount_country, symbol, exchange_rate, avg_price_country FROM unified.unified_internal_trades WHERE gt_timestamp >= to_timestamp({start_ts}) "
            f"AND gt_timestamp < to_timestamp({end_ts}) "
            f"and order_type not like 'fill_%' ORDER BY gt_timestamp LIMIT {limit} OFFSET {offset}")

class KeyDict(object):
    def __init__(self, key, dct):
        self.key = key
        self.dct = dct

    def __lt__(self, other):
        return self.key < other.key

    def __eq__(self, other):
        return self.key == other.key


class Rebate:
    def __init__(self, loop: asyncio.AbstractEventLoop):
        self.pg_pool = None
        self.fee = TradingFeesV2(loop)
        self.data_store = defaultdict(lambda : defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(int)))))
        _default_trade = defaultdict(list)
        _default_trade.setdefault("top_trades", [])
        self.trade_data_store = defaultdict(lambda : defaultdict(lambda: defaultdict( lambda: defaultdict(lambda: _default_trade))))
        self.metrics = Metrics()

    async def async_init(self, config):
        self.pg_pool = await asyncpg.create_pool(
            user=config["database"]["user"],
            password=config["database"]["password"],
            database=config["database"]["database"],
            host=config["database"]["host"],
            port=config["database"]["port"],

        )
        self.fee.pg_pool = self.pg_pool
        await self.fee.load()

    async def get_trade_from_db(self, start_time: float, end_time: float):
        conn = await self.pg_pool.acquire()
        try:
            offset = 0
            while True:
                trades = await conn.fetch(
                    format_trade_query(start_time, end_time, offset),
                )
                if len(trades) == 0:
                    break
                for trade in trades:
                    yield trade
                offset += len(trades)

        finally:
            await self.pg_pool.release(conn)

    def get_top_trade(self, trade: dict, trades: list):
        k = 1000 # track top trade count
        rebate_usdt = trade["rebate_usdt"]
        if len(trades) < k:
            heapq.heappush(trades, KeyDict(rebate_usdt, trade))
        else:
            heapq.heappushpop(trades, KeyDict(rebate_usdt, trade))
        return trades

    async def process_trades(self, start_ts: float, end_ts: float,):
        with alive_bar(title="Rebate Calculation") as bar:
            async for trade in self.get_trade_from_db(start_ts, end_ts):
                if not trade["order_type"].startswith("fill"):
                    continue
                exchange = trade["exchange"]
                symbol = trade["symbol"].replace("(", "").replace(")", "").split(",")
                fee = self.fee.get(exchange, symbol[1], timestamp=trade["gt_timestamp"].timestamp())
                maker_fee = fee["maker"]
                if maker_fee >= 0:
                    continue
                amount = float(trade["amount_country"]) * float(trade["avg_price_country"])
                rebate = abs(amount * maker_fee)
                exchange_rate = float(trade["exchange_rate"])
                if exchange_rate <= 0:
                    print(f"Exchange rate is {exchange_rate} for trade {trade['id']}")
                    continue
                if rebate <= 0:
                    continue
                bar()
                self.add_rebate_and_top_trade(exchange, symbol[0], symbol[1], rebate, rebate / exchange_rate, trade, abs(maker_fee), trade["side"], amount)

    def add_rebate_and_top_trade(self, exchange: str, base: str, quote:str, rebate: float, rebate_usdt: float, trade: dict, maker_fee: float, side: str, amount: float = 0):
        trade= dict(trade)
        self.data_store[exchange][quote][side][base]["rebate"] += rebate
        self.data_store[exchange][quote][side][base]["rebate_usdt"] += rebate_usdt
        self.data_store[exchange][quote][side][base]["accumulated_amount"] += amount

        top_trades = self.trade_data_store[exchange][quote][base][side]["top_trades"]
        trade["maker_fee"] = maker_fee
        trade["rebate"] = rebate
        trade["rebate_usdt"] = rebate_usdt
        top_trades = self.get_top_trade(trade, top_trades)
        self.trade_data_store[exchange][quote][base][side]["top_trades"] = top_trades

    def write_rebate(self, folder_name: str) -> Tuple[str, str, str]:
        rebate_file_name = "rebate.csv"
        top_trade_file_name = "top_trade.csv"
        rebate_contribution_file_name = "rebate_contribution.csv"
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
        with open(os.path.join(folder_name, rebate_file_name), "w") as csv_file:
            csv_writer = csv.DictWriter(csv_file, fieldnames=["exchange", "quote", "side", "rebate", "rebate_usdt", "average_fee"])
            csv_writer.writeheader()
            for exchange, data in self.data_store.items():
                for quote, data2 in data.items():
                    for side, data3 in data2.items():
                        rebate_exchange = 0
                        rebate_usdt_exchange = 0
                        accumulated_amount = 0
                        for base, trades in data3.items():
                            rebate_exchange += trades["rebate"]
                            rebate_usdt_exchange += trades["rebate_usdt"]
                            accumulated_amount += trades["accumulated_amount"]
                        average_fee = rebate_exchange / accumulated_amount
                        csv_writer.writerow({"exchange": exchange, "quote": quote, "rebate": rebate_exchange, "rebate_usdt": rebate_usdt_exchange,
                                             "average_fee": average_fee,"side": side})

        with open(os.path.join(folder_name, top_trade_file_name), "w") as csv_file:
            csv_writer = csv.DictWriter(csv_file, fieldnames=["exchange", "base", "quote", "side", "rebate", "rebate_usdt", "maker_fee", "gt_timestamp", "exchange_rate", "avg_price_country"])
            csv_writer.writeheader()
            for exchange, data in self.trade_data_store.items():
                for quote, data2 in data.items():
                    for side, data3 in data2.items():
                        for base, _trades in data3.items():
                            for trade in _trades["top_trades"]:
                                csv_writer.writerow({"exchange": exchange, "base": base, "quote": quote, "side": side, "rebate": trade.dct["rebate"], "rebate_usdt": trade.dct["rebate_usdt"],
                                                     "maker_fee": trade.dct["maker_fee"], "gt_timestamp": trade.dct["gt_timestamp"], "exchange_rate": trade.dct["exchange_rate"],
                                                     "avg_price_country": trade.dct["avg_price_country"]})

        with open(os.path.join(folder_name, rebate_contribution_file_name), "w") as csv_file:
            csv_writer = csv.DictWriter(csv_file, fieldnames=["exchange", "base", "quote", "side", "rebate", "rebate_usdt", "average_fee"])
            csv_writer.writeheader()
            for exchange, data in self.data_store.items():
                for quote, data2 in data.items():
                    for base, data3 in data2.items():
                        for side, trades in data3.items():
                            rebate_exchange = trades["rebate"]
                            rebate_usdt_exchange = trades["rebate_usdt"]
                            accumulated_amount = trades["accumulated_amount"]
                            average_fee = rebate_exchange / accumulated_amount
                            csv_writer.writerow({"exchange": exchange, "base": base, "quote": quote, "side": side, "rebate": rebate_exchange, "rebate_usdt": rebate_usdt_exchange,
                                                 "average_fee": average_fee})

        print(F"Rebate written in {folder_name}/{rebate_file_name}")
        print(F"Top trades written in {folder_name}/{top_trade_file_name}")
        print(F"Rebate contribution written in {folder_name}/{rebate_contribution_file_name}")
        return os.path.join(folder_name, rebate_file_name), os.path.join(folder_name, top_trade_file_name), os.path.join(folder_name, rebate_contribution_file_name)