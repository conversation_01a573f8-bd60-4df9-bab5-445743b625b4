from datetime import datetime, timezone
from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class BitkubNormalizer(Normalizer):
    exchange = "bitkub"
    def normalize_row(self, row, file_name):
        # 6/7/2024 0:00
        time_format = "%d/%m/%Y %H:%M"
        timestamp = int(datetime.strptime(row["date"], time_format).replace(tzinfo=timezone.utc).timestamp())
        asset = row['currency']
        amount = abs(float(row['coin_amount']))
        txid = row['txid']

        if "deposit" in row['type']:
            return Deposits(
                asset=asset,
                amount=amount,
                ts=timestamp,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=txid,
            )
        elif "withdraw" in row['type']:
            return Withdrawals(
                asset=asset,
                amount=amount,
                ts=timestamp,
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=txid
            )