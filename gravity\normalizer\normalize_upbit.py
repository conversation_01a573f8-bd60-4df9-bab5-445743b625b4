from gravity.normalizer.normal import Normalizer, Deposits, Withdrawals


class UpbitNormalizer(Normalizer):
    exchange = "upbit"

    def normalize_row(self, row, file_name):
        if "deposit" in file_name:
            return Deposits(
                asset=row["asset"],
                amount=float(row["amount"]),
                ts=int(float(row["requested_ts"])),
                exchange=self.exchange,
                fee=None,
                address=row["address"],
                txid=row["txid"]
            )
        elif "withdraw" in file_name:
            if row["status"] != "DONE":
                return None
            return Withdrawals(
                row["asset"],
                float(row["amount"]),
                int(float(row["requested_ts"])),
                self.exchange,
                float(row["fee"]),
                None,
                None,
                row["address"],
            )