# Daily Column Format Changes

## Overview

The hidden_balances module has been updated to support a new daily column format for Google Sheets balance tracking. Instead of replacing all data each time, the system now maintains a historical view with dates as column headers and USDT balances as values.

## New Sheet Structure

### Column Layout
- **Column A**: "Account" header with sub-account email addresses
- **Column B+**: Date headers (YYYY-MM-DD format) with USDT total balances underneath

### Example Sheet Structure
```
| Account                    | 2025-07-28 | 2025-07-29 | 2025-07-30 |
|----------------------------|------------|------------|------------|
| <EMAIL>          | $1,500.00  | $1,650.00  | $1,700.00  |
| <EMAIL>          | $2,000.00  | $2,100.00  | $2,200.00  |
| <EMAIL>          | $500.00    | $450.00    | $400.00    |
```

## Files Modified

### 1. config.py
**New Configuration Options:**
- `use_daily_columns: bool = True` - Enable daily column format
- `date_format: str = "%Y-%m-%d"` - Date format for column headers

**Updated Config Class:**
- Added new fields to the Config dataclass
- Updated load_config() function to handle new parameters
- Maintains backward compatibility

### 2. sheets_updater.py
**Major Restructuring:**

**New Methods:**
- `_get_all_account_emails()` - Extract unique emails from balances
- `_find_next_available_column()` - Find next column for today's data
- `_setup_sheet_structure()` - Initialize sheet with account emails
- `_prepare_detailed_snapshot_data()` - Prepare data for snapshots

**Updated Methods:**
- `update_balances()` - Complete rewrite for daily column format
  - Sets up sheet structure with account emails in column A
  - Finds next available column for today's date
  - Adds date header if not exists
  - Updates USDT balances for all accounts
  - Formats currency columns properly

**Key Features:**
- **Incremental Updates**: Only adds new columns, doesn't clear existing data
- **Account Management**: Automatically adds new accounts to the sheet
- **Date Detection**: Checks if today's column already exists
- **Currency Formatting**: Applies proper currency formatting to balance columns
- **Error Handling**: Comprehensive error handling with informative messages

### 3. config.template.yml & config.yml
**Added New Configuration Sections:**
```yaml
# Balance tracking configuration
use_daily_columns: true  # Use daily column format for balance tracking
date_format: "%Y-%m-%d"  # Format for date headers (YYYY-MM-DD)
```

## How It Works

### Daily Update Process
1. **Sheet Setup**: Ensures column A has "Account" header and all email addresses
2. **Column Detection**: Finds the next available column for today's data
3. **Date Header**: Adds today's date as column header (if not exists)
4. **Balance Update**: Updates USDT total balances for all accounts
5. **Formatting**: Applies currency formatting to the new column

### Account Management
- **New Accounts**: Automatically added to the bottom of the account list
- **Existing Accounts**: Balances updated in their existing rows
- **Zero Balances**: Included to maintain consistency

### Data Persistence
- **Historical Data**: Previous days' data is preserved
- **Incremental**: Only current day's column is updated
- **Snapshots**: Detailed snapshots still available with breakdown

## Usage

### Standard Daily Update
```bash
python -m balance_updater
```

### Console Only (No Sheets Update)
```bash
python -m balance_updater --console-only
```

### Create Detailed Snapshot
```bash
python -m balance_updater --snapshot
```

## Configuration

### Enable Daily Format
Set in config.yml:
```yaml
use_daily_columns: true
date_format: "%Y-%m-%d"
```

### Date Format Options
- `%Y-%m-%d`: 2025-07-29 (recommended)
- `%m/%d/%Y`: 07/29/2025
- `%d-%b-%Y`: 29-Jul-2025

## Benefits

1. **Historical Tracking**: Maintains daily balance history
2. **Data Preservation**: Never loses previous data
3. **Trend Analysis**: Easy to see balance changes over time
4. **Efficient Updates**: Only updates current day's data
5. **Scalable**: Can handle unlimited date columns
6. **Flexible**: Configurable date formats

## Backward Compatibility

- **Snapshots**: Detailed snapshots still work with full breakdown
- **Console Output**: Console display unchanged
- **Configuration**: Old configs work with default values
- **API**: All existing functionality preserved

## Testing

Run the test script to verify functionality:
```bash
python test_daily_format.py
```

The test verifies:
- Configuration loading with new parameters
- Balance data processing and mapping
- Date formatting functionality

## Deployment

The module is ready for deployment with systemd. The existing service configuration will work without changes, as the new format is controlled by configuration settings.
