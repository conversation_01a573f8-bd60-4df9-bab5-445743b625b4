import csv
import os

from collections import defaultdict
from typing import List, Optional, Set, Dict
from gravity.normalizer.normal import Transfer

class AddressBook:
    def __init__(self):
        self.addresses = defaultdict(dict)
        self.inverted_address = defaultdict(dict)

    def add_address(self, exchange: str, asset: str, address_str: str, tag: str, network: Optional[str] = None):
        address_str = address_str.upper()

        if asset not in self.addresses[exchange]:
            self.addresses[exchange][asset] = set()

        self.addresses[exchange][asset].add((address_str, tag, network))

        if asset not in self.inverted_address:
            self.inverted_address[asset] = {}

        if address_str not in self.inverted_address[asset]:
            self.inverted_address[asset][address_str] = set()

        self.inverted_address[asset][address_str].add(exchange)
        
    def load_cap1_book(self, csv_reader: csv.DictReader) -> None:
        for row in csv_reader:
            asset = row["asset"]
            exchange = row["exchange"]

            if row["tag"] == "nan":
                row["tag"] = None

            self.add_address(exchange=exchange, asset=asset, address_str=row["address"], tag=row["tag"], network=row["network"])

    def load_cap2_book(self, csv_reader: csv.DictReader, source_map_path: str):
            source_map: Dict[int, str] = {}
            with open(source_map_path) as source_map_file:
                source_map_reader = csv.DictReader(source_map_file)
                for row in source_map_reader:
                    source_map[row["gt_source"]] = row["exchange"]

            for row in csv_reader:
                self.add_address(exchange=source_map[row["gt_source"]], asset=row["gt_asset_or_alias"], address_str=row["address"], tag=row["memo"])

    def load_addresses(self, dir_path:str, source_map_path: str):
        for files in os.listdir(dir_path):
            with open(os.path.join(dir_path, files)) as csv_file:
                csv_reader = csv.DictReader(csv_file)
                if "gt_source" in csv_reader.fieldnames:
                    self.load_cap2_book(csv_reader=csv_reader, source_map_path=source_map_path)
                else:
                    self.load_cap1_book(csv_reader=csv_reader)

    def _extract_addresses_from_transfer_list(self, exchange: str, asset: str, transfer_list: List[Transfer]):
        print(f"processing {exchange} {len(transfer_list)} transfers for {asset}")
        for transfer in transfer_list:
            self.add_address(exchange=exchange, asset=asset, address_str=transfer.address, tag=transfer.tag, network=None)


    def update_from_transfers(self, transfers_by_exchange: List[Transfer], assets: List[str] = None):
        print(f"Extracting unique addresses from known transfers")
        for exchange, transfers_by_asset in transfers_by_exchange.items():
            for asset, transfer_list in transfers_by_asset.items():
                if not assets or asset in assets:
                    self._extract_addresses_from_transfer_list(exchange=exchange, asset=asset, transfer_list=transfer_list)

    def get_address(self, exchange: str, asset: str):
        return self.addresses[exchange][asset]