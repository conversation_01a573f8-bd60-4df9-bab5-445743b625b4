#!/usr/bin/env python3
"""
Main entry point for the untracked balances module.

This script orchestrates the collection of Binance sub-account balances
and updates Google Sheets with the current data.
"""

import sys
import argparse
import logging
from decimal import Decimal
from typing import Optional

from .config import get_config, Config
from .binance_client import BinanceClient
from .price_service import PriceService
from .balance_aggregator import BalanceAggregator
from .sheets_updater import SheetsUpdater


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def print_balance_summary(balances, summary_stats):
    """Print a formatted summary of balances to console."""
    print("=" * 150)
    print("SUB-ACCOUNT BALANCE SUMMARY (SPOT + MARGIN + FUTURES)")
    print("=" * 150)
    
    # Header
    print(f"{'EMAIL':<50} {'TYPE':<8} {'SPOT BTC':<15} {'SPOT USDT':<15} "
          f"{'MARGIN BTC':<15} {'MARGIN USDT':<15} {'FUT BTC':<15} {'FUT USDT':<15} "
          f"{'TOTAL BTC':<15} {'TOTAL USDT':<15}")
    print("-" * 150)
    
    # Data rows
    for balance in balances:
        # Show accounts based on filtering configuration
        filter_emails = set(get_config().emails) if get_config().emails else None
        show_account = filter_emails or balance.total_btc > 0
        
        if show_account:
            print(f"{balance.email:<50} {balance.account_type:<8} "
                  f"{balance.spot_btc:<15} {balance.spot_usdt:<15} "
                  f"{balance.margin_btc:<15} {balance.margin_usdt:<15} "
                  f"{balance.futures_btc:<15} {balance.futures_usdt:<15} "
                  f"{balance.total_btc:<15} {balance.total_usdt:<15}")
    
    print("-" * 150)
    print(f"{'GRAND TOTAL':<50} {'':<8} {'':<15} {'':<15} {'':<15} {'':<15} "
          f"{'':<15} {'':<15} {summary_stats['grand_total_btc']:<15} "
          f"{summary_stats['grand_total_usdt']:<15}")
    print("=" * 150)
    
    # Summary stats
    print(f"\nSUMMARY:")
    print(f"  Active Accounts: {summary_stats['total_accounts']} "
          f"({summary_stats['regular_accounts']} regular + {summary_stats['managed_accounts']} managed)")
    print(f"  Total Portfolio: {summary_stats['grand_total_btc']} BTC ≈ "
          f"${summary_stats['grand_total_usdt']:,} USDT")
    print(f"  BTC Price: ${summary_stats['btc_price']:,}")
    print(f"  Timestamp: {summary_stats['timestamp']}")
    print("=" * 150)


def main():
    """Main function to orchestrate balance collection and updates."""
    parser = argparse.ArgumentParser(description="Collect Binance sub-account balances")
    parser.add_argument("--config", default="config.yml", help="Path to configuration file")
    parser.add_argument("--no-sheets", action="store_true", help="Skip Google Sheets update")
    parser.add_argument("--snapshot", action="store_true", help="Create historical snapshot")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("--console-only", action="store_true", help="Only print to console, no sheets update")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        logger.info("Loading configuration...")
        config = get_config()
        
        # Initialize services
        logger.info("Initializing Binance client...")
        binance_client = BinanceClient(config)
        
        logger.info("Initializing price service...")
        price_service = PriceService(binance_client)
        
        logger.info("Initializing balance aggregator...")
        balance_aggregator = BalanceAggregator(config, binance_client, price_service)
        
        # Collect balances
        logger.info("Collecting account balances...")
        balances = balance_aggregator.collect_all_balances()
        
        # Generate summary statistics
        logger.info("Generating summary statistics...")
        summary_stats = balance_aggregator.get_summary_stats(balances)
        
        # Print console summary
        print_balance_summary(balances, summary_stats)
        
        # Update Google Sheets (unless disabled)
        if not args.console_only and not args.no_sheets:
            try:
                logger.info("Initializing Google Sheets updater...")
                sheets_updater = SheetsUpdater(config)
                
                logger.info("Updating Google Sheets...")
                success = sheets_updater.update_balances(balances, summary_stats)
                
                if success:
                    logger.info("Google Sheets updated successfully")
                else:
                    logger.error("Failed to update Google Sheets")
                
                # Create historical snapshot if requested
                if args.snapshot:
                    logger.info("Creating historical snapshot...")
                    snapshot_success = sheets_updater.create_historical_snapshot(balances, summary_stats)
                    if snapshot_success:
                        logger.info("Historical snapshot created successfully")
                    else:
                        logger.error("Failed to create historical snapshot")
                        
            except Exception as e:
                logger.error(f"Google Sheets integration error: {e}")
                if not args.console_only:
                    logger.info("Continuing without Google Sheets update...")
        
        logger.info("Balance collection completed successfully")
        
    except Exception as e:
        logger.error(f"Error during execution: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
