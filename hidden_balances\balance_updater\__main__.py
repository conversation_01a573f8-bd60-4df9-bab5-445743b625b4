#!/usr/bin/env python3
import sys
import argparse
import logging
from .config import get_config
from .binance_client import BinanceClient
from .price_service import PriceService
from .balance_aggregator import BalanceAggregator

from .sheets_updater_direct import DirectSheetsUpdater


def setup_logging(verbose: bool = False):
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def print_balance_summary(balances, summary_stats):
    """Print a formatted summary of balances to console."""
    print("=" * 150)
    print("SUB-ACCOUNT BALANCE SUMMARY (SPOT + MARGIN + FUTURES)")
    print("=" * 150)

    print(f"{'EMAIL':<50} {'TYPE':<8} {'SPOT BTC':<15} {'SPOT USDT':<15} "
          f"{'MARGIN BTC':<15} {'MARGIN USDT':<15} {'FUT BTC':<15} {'FUT USDT':<15} "
          f"{'TOTAL BTC':<15} {'TOTAL USDT':<15}")
    print("-" * 150)

    for balance in balances:
        filter_emails = set(get_config().emails) if get_config().emails else None
        show_account = filter_emails or balance.total_btc > 0

        if show_account:
            print(f"{balance.email:<50} {balance.account_type:<8} "
                  f"{balance.spot_btc:<15} {balance.spot_usdt:<15} "
                  f"{balance.margin_btc:<15} {balance.margin_usdt:<15} "
                  f"{balance.futures_btc:<15} {balance.futures_usdt:<15} "
                  f"{balance.total_btc:<15} {balance.total_usdt:<15}")

    print("-" * 150)
    print(f"{'GRAND TOTAL':<50} {'':<8} {'':<15} {'':<15} {'':<15} {'':<15} "
          f"{'':<15} {'':<15} {summary_stats['grand_total_btc']:<15} "
          f"{summary_stats['grand_total_usdt']:<15}")
    print("=" * 150)

    print(f"\nSUMMARY:")
    print(f"  Active Accounts: {summary_stats['total_accounts']} "
          f"({summary_stats['regular_accounts']} regular + {summary_stats['managed_accounts']} managed)")
    print(f"  Total Portfolio: {summary_stats['grand_total_btc']} BTC ≈ "
          f"${summary_stats['grand_total_usdt']:,} USDT")
    print(f"  BTC Price: ${summary_stats['btc_price']:,}")
    print(f"  Timestamp: {summary_stats['timestamp']}")
    print("=" * 150)


def main():
    """Main function to orchestrate balance collection and updates."""
    parser = argparse.ArgumentParser(description="Collect Binance sub-account balances")
    parser.add_argument("--config", default="config.yml", help="Path to configuration file")
    parser.add_argument("--no-sheets", action="store_true", help="Skip Google Sheets update")
    parser.add_argument("--snapshot", action="store_true", help="Create historical snapshot")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("--console-only", action="store_true", help="Only print to console, no sheets update")

    args = parser.parse_args()
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)

    try:
        logger.info("Loading configuration...")
        config = get_config()

        logger.info("Initializing services...")
        binance_client = BinanceClient(config)
        price_service = PriceService(binance_client)
        balance_aggregator = BalanceAggregator(config, binance_client, price_service)

        logger.info("Collecting account balances...")
        balances = balance_aggregator.collect_all_balances()
        summary_stats = balance_aggregator.get_summary_stats(balances)

        print_balance_summary(balances, summary_stats)
        
        if not args.console_only and not args.no_sheets:
            try:
                logger.info("Updating Google Sheets...")
                sheets_updater = DirectSheetsUpdater(config)
                success = sheets_updater.update_balances(balances, summary_stats)

                if success:
                    logger.info("Google Sheets updated successfully")
                else:
                    logger.error("Failed to update Google Sheets")

                if args.snapshot:
                    logger.info("Snapshot functionality not implemented with direct API")

            except Exception as e:
                logger.error(f"Google Sheets integration error: {e}")
                logger.info("Continuing without Google Sheets update...")

        logger.info("Balance collection completed successfully")

    except Exception as e:
        logger.error(f"Error during execution: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
