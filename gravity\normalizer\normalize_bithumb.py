from datetime import datetime

from gravity.normalizer.normal import Withdrawals, Deposits, Normalizer


def bithumb_to_unified(asset):
    if asset == "STRAX_OLD":
        return "STRAX"
    return asset

class BithumbNormalizer(Normalizer):
    exchange = "bithumb"
    def normalize_row(self, row, file_name: str):
        if "deposit" in file_name:
            return Deposits(
                asset=bithumb_to_unified(row["asset"]),
                amount=float(row["amount"]),
                ts=row["requested_ts"],
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=row["txid"],
            )
        elif "withdrawals" in file_name:
            return Withdrawals(
                asset=bithumb_to_unified(row["asset"]),
                amount=float(row["amount"]),
                ts=row["requested_ts"],
                exchange=self.exchange,
                fee=None,
                address=None,
                tag=None,
                txid=row["txid"],
            )