import csv
import dataclasses
import os
from typing import Final, NewType, Tuple, List

from gravity.normalizer.normal import Transfer, Withdrawals, Deposits

Address: Final = NewType("Address", Tuple[str, str])


class IOHandler:
    def __init__(self, input_path: str, output_path: str):
        self.input_path = input_path
        self.output_path = output_path

    def _get_withdrawal_file(self, exchange: str):
        for file in os.listdir(self.input_path):
            if file.startswith(exchange) and file.endswith("withdrawals.csv"):
                return file

    def get_exchange_asset_withdrawals(self, exchange: str, asset: str):
        withdrawal_file = self._get_withdrawal_file(exchange)
        if not withdrawal_file:
            return []
        with open(os.path.join(self.input_path, withdrawal_file)) as csv_file:
            csv_reader = csv.DictReader(csv_file)
            for row in csv_reader:
                if row["asset"] == asset:
                    yield Transfer(*row)

    def _get_deposit_file(self, exchange: str):
        for file in os.listdir(self.input_path):
            if file.startswith(exchange) and file.endswith("deposits.csv"):
                return file

    def get_exchange_asset_deposits(self, exchange: str, asset: str):
        deposit_file = self._get_deposit_file(exchange)
        with open(os.path.join(self.input_path, deposit_file)) as csv_file:
            csv_reader = csv.DictReader(csv_file)
            for row in csv_reader:
                if row["asset"] == asset:
                    yield Transfer(*row)

    def write_matched_transfers(
        self, matched_transfers: List[Tuple[Withdrawals, Deposits]]
    ):
        with open(
            os.path.join(self.output_path, "matched_withdrawal.csv"), "a"
        ) as withdrawal_file:
            csv_with_writer = csv.DictWriter(
                withdrawal_file,
                fieldnames=Withdrawals.__dataclass_fields__.keys(),
            )
            csv_with_writer.writeheader()
            with open(
                os.path.join(self.output_path, "matched_deposit.csv"), "a"
            ) as deposit_file:
                csv_dep_writer = csv.DictWriter(
                    deposit_file,
                    fieldnames=Deposits.__dataclass_fields__.keys(),
                )
                csv_dep_writer.writeheader()
                for withdrawal, deposit in matched_transfers:
                    csv_with_writer.writerow(dataclasses.asdict(withdrawal))
                    csv_dep_writer.writerow(dataclasses.asdict(deposit))

    def write_unmatched_withdrawals(self, unmatched_withdrawals: List[Withdrawals]):
        with open(
            os.path.join(self.output_path, "unmatched_withdrawal.csv"), "a"
        ) as withdrawal_file:
            csv_writer = csv.DictWriter(
                withdrawal_file,
                fieldnames=Withdrawals.__dataclass_fields__.keys(),
            )
            csv_writer.writeheader()
            for withdrawal in unmatched_withdrawals:
                csv_writer.writerow(dataclasses.asdict(withdrawal))

    def write_unmatched_deposits(self, unmatched_deposits: List[Deposits]):
        with open(
            os.path.join(self.output_path, "unmatched_deposit.csv"), "a"
        ) as deposit_file:
            csv_writer = csv.DictWriter(
                deposit_file,
                fieldnames=Deposits.__dataclass_fields__.keys(),
            )
            csv_writer.writeheader()
            for deposit in unmatched_deposits:
                csv_writer.writerow(dataclasses.asdict(deposit))
